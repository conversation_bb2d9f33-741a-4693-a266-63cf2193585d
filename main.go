package main

import (
	"log"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv" // 引入 godotenv 包
)

func main() {
	// 1. 加载 .env 文件中的环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("Warning: .env file not found, using environment variables from OS")
	}

	// 从环境变量中读取凭证
	expectedUser := os.Getenv("WEBHOOK_USER")
	expectedPass := os.Getenv("WEBHOOK_PASS")
	expectedToken := os.Getenv("WEBHOOK_BEARER_TOKEN")

	if expectedUser == "" || expectedPass == "" || expectedToken == "" {
		log.Fatal("Error: Required environment variables (WEBHOOK_USER, WEBHOOK_PASS, WEBHOOK_BEARER_TOKEN) are not set.")
	}

	// 2. 初始化 Gin 引擎
	router := gin.Default()

	// 3. 设置路由并应用中间件
	// 创建一个 API 分组，并将我们的认证中间件应用到这个分组的所有路由上
	api := router.Group("/api/v1")
	api.Use(AuthMiddleware(expectedUser, expectedPass, expectedToken))
	{
		// 只有通过了 AuthMiddleware 的请求才能到达 HandleWebhook
		api.POST("/webhook", HandleWebhook)
	}

	// 4. 定义服务器端口并启动
	port := "8080"
	log.Printf("Starting Gin Webhook Receiver on port %s", port)
	log.Printf("Listening for alerts at http://localhost:%s/api/v1/webhook", port)

	if err := router.Run(":" + port); err != nil {
		log.Fatalf("Failed to run server: %v", err)
	}
}
