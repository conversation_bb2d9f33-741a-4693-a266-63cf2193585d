// Code generated by mkmerge; DO NOT EDIT.

//go:build linux

package unix

import "syscall"

const (
	AAFS_MAGIC                                  = 0x5a3c69f0
	ADFS_SUPER_MAGIC                            = 0xadf5
	AFFS_SUPER_MAGIC                            = 0xadff
	AFS_FS_MAGIC                                = 0x6b414653
	AFS_SUPER_MAGIC                             = 0x5346414f
	AF_ALG                                      = 0x26
	AF_APPLETALK                                = 0x5
	AF_ASH                                      = 0x12
	AF_ATMPVC                                   = 0x8
	AF_ATMSVC                                   = 0x14
	AF_AX25                                     = 0x3
	AF_BLUETOOTH                                = 0x1f
	AF_BRIDGE                                   = 0x7
	AF_CAIF                                     = 0x25
	AF_CAN                                      = 0x1d
	AF_DECnet                                   = 0xc
	AF_ECONET                                   = 0x13
	AF_FILE                                     = 0x1
	AF_IB                                       = 0x1b
	AF_IEEE802154                               = 0x24
	AF_INET                                     = 0x2
	AF_INET6                                    = 0xa
	AF_IPX                                      = 0x4
	AF_IRDA                                     = 0x17
	AF_ISDN                                     = 0x22
	AF_IUCV                                     = 0x20
	AF_KCM                                      = 0x29
	AF_KEY                                      = 0xf
	AF_LLC                                      = 0x1a
	AF_LOCAL                                    = 0x1
	AF_MAX                                      = 0x2e
	AF_MCTP                                     = 0x2d
	AF_MPLS                                     = 0x1c
	AF_NETBEUI                                  = 0xd
	AF_NETLINK                                  = 0x10
	AF_NETROM                                   = 0x6
	AF_NFC                                      = 0x27
	AF_PACKET                                   = 0x11
	AF_PHONET                                   = 0x23
	AF_PPPOX                                    = 0x18
	AF_QIPCRTR                                  = 0x2a
	AF_RDS                                      = 0x15
	AF_ROSE                                     = 0xb
	AF_ROUTE                                    = 0x10
	AF_RXRPC                                    = 0x21
	AF_SECURITY                                 = 0xe
	AF_SMC                                      = 0x2b
	AF_SNA                                      = 0x16
	AF_TIPC                                     = 0x1e
	AF_UNIX                                     = 0x1
	AF_UNSPEC                                   = 0x0
	AF_VSOCK                                    = 0x28
	AF_WANPIPE                                  = 0x19
	AF_X25                                      = 0x9
	AF_XDP                                      = 0x2c
	ALG_OP_DECRYPT                              = 0x0
	ALG_OP_ENCRYPT                              = 0x1
	ALG_SET_AEAD_ASSOCLEN                       = 0x4
	ALG_SET_AEAD_AUTHSIZE                       = 0x5
	ALG_SET_DRBG_ENTROPY                        = 0x6
	ALG_SET_IV                                  = 0x2
	ALG_SET_KEY                                 = 0x1
	ALG_SET_KEY_BY_KEY_SERIAL                   = 0x7
	ALG_SET_OP                                  = 0x3
	ANON_INODE_FS_MAGIC                         = 0x9041934
	ARPHRD_6LOWPAN                              = 0x339
	ARPHRD_ADAPT                                = 0x108
	ARPHRD_APPLETLK                             = 0x8
	ARPHRD_ARCNET                               = 0x7
	ARPHRD_ASH                                  = 0x30d
	ARPHRD_ATM                                  = 0x13
	ARPHRD_AX25                                 = 0x3
	ARPHRD_BIF                                  = 0x307
	ARPHRD_CAIF                                 = 0x336
	ARPHRD_CAN                                  = 0x118
	ARPHRD_CHAOS                                = 0x5
	ARPHRD_CISCO                                = 0x201
	ARPHRD_CSLIP                                = 0x101
	ARPHRD_CSLIP6                               = 0x103
	ARPHRD_DDCMP                                = 0x205
	ARPHRD_DLCI                                 = 0xf
	ARPHRD_ECONET                               = 0x30e
	ARPHRD_EETHER                               = 0x2
	ARPHRD_ETHER                                = 0x1
	ARPHRD_EUI64                                = 0x1b
	ARPHRD_FCAL                                 = 0x311
	ARPHRD_FCFABRIC                             = 0x313
	ARPHRD_FCPL                                 = 0x312
	ARPHRD_FCPP                                 = 0x310
	ARPHRD_FDDI                                 = 0x306
	ARPHRD_FRAD                                 = 0x302
	ARPHRD_HDLC                                 = 0x201
	ARPHRD_HIPPI                                = 0x30c
	ARPHRD_HWX25                                = 0x110
	ARPHRD_IEEE1394                             = 0x18
	ARPHRD_IEEE802                              = 0x6
	ARPHRD_IEEE80211                            = 0x321
	ARPHRD_IEEE80211_PRISM                      = 0x322
	ARPHRD_IEEE80211_RADIOTAP                   = 0x323
	ARPHRD_IEEE802154                           = 0x324
	ARPHRD_IEEE802154_MONITOR                   = 0x325
	ARPHRD_IEEE802_TR                           = 0x320
	ARPHRD_INFINIBAND                           = 0x20
	ARPHRD_IP6GRE                               = 0x337
	ARPHRD_IPDDP                                = 0x309
	ARPHRD_IPGRE                                = 0x30a
	ARPHRD_IRDA                                 = 0x30f
	ARPHRD_LAPB                                 = 0x204
	ARPHRD_LOCALTLK                             = 0x305
	ARPHRD_LOOPBACK                             = 0x304
	ARPHRD_MCTP                                 = 0x122
	ARPHRD_METRICOM                             = 0x17
	ARPHRD_NETLINK                              = 0x338
	ARPHRD_NETROM                               = 0x0
	ARPHRD_NONE                                 = 0xfffe
	ARPHRD_PHONET                               = 0x334
	ARPHRD_PHONET_PIPE                          = 0x335
	ARPHRD_PIMREG                               = 0x30b
	ARPHRD_PPP                                  = 0x200
	ARPHRD_PRONET                               = 0x4
	ARPHRD_RAWHDLC                              = 0x206
	ARPHRD_RAWIP                                = 0x207
	ARPHRD_ROSE                                 = 0x10e
	ARPHRD_RSRVD                                = 0x104
	ARPHRD_SIT                                  = 0x308
	ARPHRD_SKIP                                 = 0x303
	ARPHRD_SLIP                                 = 0x100
	ARPHRD_SLIP6                                = 0x102
	ARPHRD_TUNNEL                               = 0x300
	ARPHRD_TUNNEL6                              = 0x301
	ARPHRD_VOID                                 = 0xffff
	ARPHRD_VSOCKMON                             = 0x33a
	ARPHRD_X25                                  = 0x10f
	AUDIT_ADD                                   = 0x3eb
	AUDIT_ADD_RULE                              = 0x3f3
	AUDIT_ALWAYS                                = 0x2
	AUDIT_ANOM_ABEND                            = 0x6a5
	AUDIT_ANOM_CREAT                            = 0x6a7
	AUDIT_ANOM_LINK                             = 0x6a6
	AUDIT_ANOM_PROMISCUOUS                      = 0x6a4
	AUDIT_ARCH                                  = 0xb
	AUDIT_ARCH_AARCH64                          = 0xc00000b7
	AUDIT_ARCH_ALPHA                            = 0xc0009026
	AUDIT_ARCH_ARCOMPACT                        = 0x4000005d
	AUDIT_ARCH_ARCOMPACTBE                      = 0x5d
	AUDIT_ARCH_ARCV2                            = 0x400000c3
	AUDIT_ARCH_ARCV2BE                          = 0xc3
	AUDIT_ARCH_ARM                              = 0x40000028
	AUDIT_ARCH_ARMEB                            = 0x28
	AUDIT_ARCH_C6X                              = 0x4000008c
	AUDIT_ARCH_C6XBE                            = 0x8c
	AUDIT_ARCH_CRIS                             = 0x4000004c
	AUDIT_ARCH_CSKY                             = 0x400000fc
	AUDIT_ARCH_FRV                              = 0x5441
	AUDIT_ARCH_H8300                            = 0x2e
	AUDIT_ARCH_HEXAGON                          = 0xa4
	AUDIT_ARCH_I386                             = 0x40000003
	AUDIT_ARCH_IA64                             = 0xc0000032
	AUDIT_ARCH_LOONGARCH32                      = 0x40000102
	AUDIT_ARCH_LOONGARCH64                      = 0xc0000102
	AUDIT_ARCH_M32R                             = 0x58
	AUDIT_ARCH_M68K                             = 0x4
	AUDIT_ARCH_MICROBLAZE                       = 0xbd
	AUDIT_ARCH_MIPS                             = 0x8
	AUDIT_ARCH_MIPS64                           = 0x80000008
	AUDIT_ARCH_MIPS64N32                        = 0xa0000008
	AUDIT_ARCH_MIPSEL                           = 0x40000008
	AUDIT_ARCH_MIPSEL64                         = 0xc0000008
	AUDIT_ARCH_MIPSEL64N32                      = 0xe0000008
	AUDIT_ARCH_NDS32                            = 0x400000a7
	AUDIT_ARCH_NDS32BE                          = 0xa7
	AUDIT_ARCH_NIOS2                            = 0x40000071
	AUDIT_ARCH_OPENRISC                         = 0x5c
	AUDIT_ARCH_PARISC                           = 0xf
	AUDIT_ARCH_PARISC64                         = 0x8000000f
	AUDIT_ARCH_PPC                              = 0x14
	AUDIT_ARCH_PPC64                            = 0x80000015
	AUDIT_ARCH_PPC64LE                          = 0xc0000015
	AUDIT_ARCH_RISCV32                          = 0x400000f3
	AUDIT_ARCH_RISCV64                          = 0xc00000f3
	AUDIT_ARCH_S390                             = 0x16
	AUDIT_ARCH_S390X                            = 0x80000016
	AUDIT_ARCH_SH                               = 0x2a
	AUDIT_ARCH_SH64                             = 0x8000002a
	AUDIT_ARCH_SHEL                             = 0x4000002a
	AUDIT_ARCH_SHEL64                           = 0xc000002a
	AUDIT_ARCH_SPARC                            = 0x2
	AUDIT_ARCH_SPARC64                          = 0x8000002b
	AUDIT_ARCH_TILEGX                           = 0xc00000bf
	AUDIT_ARCH_TILEGX32                         = 0x400000bf
	AUDIT_ARCH_TILEPRO                          = 0x400000bc
	AUDIT_ARCH_UNICORE                          = 0x4000006e
	AUDIT_ARCH_X86_64                           = 0xc000003e
	AUDIT_ARCH_XTENSA                           = 0x5e
	AUDIT_ARG0                                  = 0xc8
	AUDIT_ARG1                                  = 0xc9
	AUDIT_ARG2                                  = 0xca
	AUDIT_ARG3                                  = 0xcb
	AUDIT_AVC                                   = 0x578
	AUDIT_AVC_PATH                              = 0x57a
	AUDIT_BITMASK_SIZE                          = 0x40
	AUDIT_BIT_MASK                              = 0x8000000
	AUDIT_BIT_TEST                              = 0x48000000
	AUDIT_BPF                                   = 0x536
	AUDIT_BPRM_FCAPS                            = 0x529
	AUDIT_CAPSET                                = 0x52a
	AUDIT_CLASS_CHATTR                          = 0x2
	AUDIT_CLASS_CHATTR_32                       = 0x3
	AUDIT_CLASS_DIR_WRITE                       = 0x0
	AUDIT_CLASS_DIR_WRITE_32                    = 0x1
	AUDIT_CLASS_READ                            = 0x4
	AUDIT_CLASS_READ_32                         = 0x5
	AUDIT_CLASS_SIGNAL                          = 0x8
	AUDIT_CLASS_SIGNAL_32                       = 0x9
	AUDIT_CLASS_WRITE                           = 0x6
	AUDIT_CLASS_WRITE_32                        = 0x7
	AUDIT_COMPARE_AUID_TO_EUID                  = 0x10
	AUDIT_COMPARE_AUID_TO_FSUID                 = 0xe
	AUDIT_COMPARE_AUID_TO_OBJ_UID               = 0x5
	AUDIT_COMPARE_AUID_TO_SUID                  = 0xf
	AUDIT_COMPARE_EGID_TO_FSGID                 = 0x17
	AUDIT_COMPARE_EGID_TO_OBJ_GID               = 0x4
	AUDIT_COMPARE_EGID_TO_SGID                  = 0x18
	AUDIT_COMPARE_EUID_TO_FSUID                 = 0x12
	AUDIT_COMPARE_EUID_TO_OBJ_UID               = 0x3
	AUDIT_COMPARE_EUID_TO_SUID                  = 0x11
	AUDIT_COMPARE_FSGID_TO_OBJ_GID              = 0x9
	AUDIT_COMPARE_FSUID_TO_OBJ_UID              = 0x8
	AUDIT_COMPARE_GID_TO_EGID                   = 0x14
	AUDIT_COMPARE_GID_TO_FSGID                  = 0x15
	AUDIT_COMPARE_GID_TO_OBJ_GID                = 0x2
	AUDIT_COMPARE_GID_TO_SGID                   = 0x16
	AUDIT_COMPARE_SGID_TO_FSGID                 = 0x19
	AUDIT_COMPARE_SGID_TO_OBJ_GID               = 0x7
	AUDIT_COMPARE_SUID_TO_FSUID                 = 0x13
	AUDIT_COMPARE_SUID_TO_OBJ_UID               = 0x6
	AUDIT_COMPARE_UID_TO_AUID                   = 0xa
	AUDIT_COMPARE_UID_TO_EUID                   = 0xb
	AUDIT_COMPARE_UID_TO_FSUID                  = 0xc
	AUDIT_COMPARE_UID_TO_OBJ_UID                = 0x1
	AUDIT_COMPARE_UID_TO_SUID                   = 0xd
	AUDIT_CONFIG_CHANGE                         = 0x519
	AUDIT_CWD                                   = 0x51b
	AUDIT_DAEMON_ABORT                          = 0x4b2
	AUDIT_DAEMON_CONFIG                         = 0x4b3
	AUDIT_DAEMON_END                            = 0x4b1
	AUDIT_DAEMON_START                          = 0x4b0
	AUDIT_DEL                                   = 0x3ec
	AUDIT_DEL_RULE                              = 0x3f4
	AUDIT_DEVMAJOR                              = 0x64
	AUDIT_DEVMINOR                              = 0x65
	AUDIT_DIR                                   = 0x6b
	AUDIT_DM_CTRL                               = 0x53a
	AUDIT_DM_EVENT                              = 0x53b
	AUDIT_EGID                                  = 0x6
	AUDIT_EOE                                   = 0x528
	AUDIT_EQUAL                                 = 0x40000000
	AUDIT_EUID                                  = 0x2
	AUDIT_EVENT_LISTENER                        = 0x537
	AUDIT_EXE                                   = 0x70
	AUDIT_EXECVE                                = 0x51d
	AUDIT_EXIT                                  = 0x67
	AUDIT_FAIL_PANIC                            = 0x2
	AUDIT_FAIL_PRINTK                           = 0x1
	AUDIT_FAIL_SILENT                           = 0x0
	AUDIT_FANOTIFY                              = 0x533
	AUDIT_FD_PAIR                               = 0x525
	AUDIT_FEATURE_BITMAP_ALL                    = 0x7f
	AUDIT_FEATURE_BITMAP_BACKLOG_LIMIT          = 0x1
	AUDIT_FEATURE_BITMAP_BACKLOG_WAIT_TIME      = 0x2
	AUDIT_FEATURE_BITMAP_EXCLUDE_EXTEND         = 0x8
	AUDIT_FEATURE_BITMAP_EXECUTABLE_PATH        = 0x4
	AUDIT_FEATURE_BITMAP_FILTER_FS              = 0x40
	AUDIT_FEATURE_BITMAP_LOST_RESET             = 0x20
	AUDIT_FEATURE_BITMAP_SESSIONID_FILTER       = 0x10
	AUDIT_FEATURE_CHANGE                        = 0x530
	AUDIT_FEATURE_LOGINUID_IMMUTABLE            = 0x1
	AUDIT_FEATURE_ONLY_UNSET_LOGINUID           = 0x0
	AUDIT_FEATURE_VERSION                       = 0x1
	AUDIT_FIELD_COMPARE                         = 0x6f
	AUDIT_FILETYPE                              = 0x6c
	AUDIT_FILTERKEY                             = 0xd2
	AUDIT_FILTER_ENTRY                          = 0x2
	AUDIT_FILTER_EXCLUDE                        = 0x5
	AUDIT_FILTER_EXIT                           = 0x4
	AUDIT_FILTER_FS                             = 0x6
	AUDIT_FILTER_PREPEND                        = 0x10
	AUDIT_FILTER_TASK                           = 0x1
	AUDIT_FILTER_TYPE                           = 0x5
	AUDIT_FILTER_URING_EXIT                     = 0x7
	AUDIT_FILTER_USER                           = 0x0
	AUDIT_FILTER_WATCH                          = 0x3
	AUDIT_FIRST_KERN_ANOM_MSG                   = 0x6a4
	AUDIT_FIRST_USER_MSG                        = 0x44c
	AUDIT_FIRST_USER_MSG2                       = 0x834
	AUDIT_FSGID                                 = 0x8
	AUDIT_FSTYPE                                = 0x1a
	AUDIT_FSUID                                 = 0x4
	AUDIT_GET                                   = 0x3e8
	AUDIT_GET_FEATURE                           = 0x3fb
	AUDIT_GID                                   = 0x5
	AUDIT_GREATER_THAN                          = 0x20000000
	AUDIT_GREATER_THAN_OR_EQUAL                 = 0x60000000
	AUDIT_INODE                                 = 0x66
	AUDIT_INTEGRITY_DATA                        = 0x708
	AUDIT_INTEGRITY_EVM_XATTR                   = 0x70e
	AUDIT_INTEGRITY_HASH                        = 0x70b
	AUDIT_INTEGRITY_METADATA                    = 0x709
	AUDIT_INTEGRITY_PCR                         = 0x70c
	AUDIT_INTEGRITY_POLICY_RULE                 = 0x70f
	AUDIT_INTEGRITY_RULE                        = 0x70d
	AUDIT_INTEGRITY_STATUS                      = 0x70a
	AUDIT_IPC                                   = 0x517
	AUDIT_IPC_SET_PERM                          = 0x51f
	AUDIT_KERNEL                                = 0x7d0
	AUDIT_KERNEL_OTHER                          = 0x524
	AUDIT_KERN_MODULE                           = 0x532
	AUDIT_LAST_FEATURE                          = 0x1
	AUDIT_LAST_KERN_ANOM_MSG                    = 0x707
	AUDIT_LAST_USER_MSG                         = 0x4af
	AUDIT_LAST_USER_MSG2                        = 0xbb7
	AUDIT_LESS_THAN                             = 0x10000000
	AUDIT_LESS_THAN_OR_EQUAL                    = 0x50000000
	AUDIT_LIST                                  = 0x3ea
	AUDIT_LIST_RULES                            = 0x3f5
	AUDIT_LOGIN                                 = 0x3ee
	AUDIT_LOGINUID                              = 0x9
	AUDIT_LOGINUID_SET                          = 0x18
	AUDIT_MAC_CALIPSO_ADD                       = 0x58a
	AUDIT_MAC_CALIPSO_DEL                       = 0x58b
	AUDIT_MAC_CIPSOV4_ADD                       = 0x57f
	AUDIT_MAC_CIPSOV4_DEL                       = 0x580
	AUDIT_MAC_CONFIG_CHANGE                     = 0x57d
	AUDIT_MAC_IPSEC_ADDSA                       = 0x583
	AUDIT_MAC_IPSEC_ADDSPD                      = 0x585
	AUDIT_MAC_IPSEC_DELSA                       = 0x584
	AUDIT_MAC_IPSEC_DELSPD                      = 0x586
	AUDIT_MAC_IPSEC_EVENT                       = 0x587
	AUDIT_MAC_MAP_ADD                           = 0x581
	AUDIT_MAC_MAP_DEL                           = 0x582
	AUDIT_MAC_POLICY_LOAD                       = 0x57b
	AUDIT_MAC_STATUS                            = 0x57c
	AUDIT_MAC_UNLBL_ALLOW                       = 0x57e
	AUDIT_MAC_UNLBL_STCADD                      = 0x588
	AUDIT_MAC_UNLBL_STCDEL                      = 0x589
	AUDIT_MAKE_EQUIV                            = 0x3f7
	AUDIT_MAX_FIELDS                            = 0x40
	AUDIT_MAX_FIELD_COMPARE                     = 0x19
	AUDIT_MAX_KEY_LEN                           = 0x100
	AUDIT_MESSAGE_TEXT_MAX                      = 0x2170
	AUDIT_MMAP                                  = 0x52b
	AUDIT_MQ_GETSETATTR                         = 0x523
	AUDIT_MQ_NOTIFY                             = 0x522
	AUDIT_MQ_OPEN                               = 0x520
	AUDIT_MQ_SENDRECV                           = 0x521
	AUDIT_MSGTYPE                               = 0xc
	AUDIT_NEGATE                                = 0x80000000
	AUDIT_NETFILTER_CFG                         = 0x52d
	AUDIT_NETFILTER_PKT                         = 0x52c
	AUDIT_NEVER                                 = 0x0
	AUDIT_NLGRP_MAX                             = 0x1
	AUDIT_NOT_EQUAL                             = 0x30000000
	AUDIT_NR_FILTERS                            = 0x8
	AUDIT_OBJ_GID                               = 0x6e
	AUDIT_OBJ_LEV_HIGH                          = 0x17
	AUDIT_OBJ_LEV_LOW                           = 0x16
	AUDIT_OBJ_PID                               = 0x526
	AUDIT_OBJ_ROLE                              = 0x14
	AUDIT_OBJ_TYPE                              = 0x15
	AUDIT_OBJ_UID                               = 0x6d
	AUDIT_OBJ_USER                              = 0x13
	AUDIT_OPENAT2                               = 0x539
	AUDIT_OPERATORS                             = 0x78000000
	AUDIT_PATH                                  = 0x516
	AUDIT_PERM                                  = 0x6a
	AUDIT_PERM_ATTR                             = 0x8
	AUDIT_PERM_EXEC                             = 0x1
	AUDIT_PERM_READ                             = 0x4
	AUDIT_PERM_WRITE                            = 0x2
	AUDIT_PERS                                  = 0xa
	AUDIT_PID                                   = 0x0
	AUDIT_POSSIBLE                              = 0x1
	AUDIT_PPID                                  = 0x12
	AUDIT_PROCTITLE                             = 0x52f
	AUDIT_REPLACE                               = 0x531
	AUDIT_SADDR_FAM                             = 0x71
	AUDIT_SECCOMP                               = 0x52e
	AUDIT_SELINUX_ERR                           = 0x579
	AUDIT_SESSIONID                             = 0x19
	AUDIT_SET                                   = 0x3e9
	AUDIT_SET_FEATURE                           = 0x3fa
	AUDIT_SGID                                  = 0x7
	AUDIT_SID_UNSET                             = 0xffffffff
	AUDIT_SIGNAL_INFO                           = 0x3f2
	AUDIT_SOCKADDR                              = 0x51a
	AUDIT_SOCKETCALL                            = 0x518
	AUDIT_STATUS_BACKLOG_LIMIT                  = 0x10
	AUDIT_STATUS_BACKLOG_WAIT_TIME              = 0x20
	AUDIT_STATUS_BACKLOG_WAIT_TIME_ACTUAL       = 0x80
	AUDIT_STATUS_ENABLED                        = 0x1
	AUDIT_STATUS_FAILURE                        = 0x2
	AUDIT_STATUS_LOST                           = 0x40
	AUDIT_STATUS_PID                            = 0x4
	AUDIT_STATUS_RATE_LIMIT                     = 0x8
	AUDIT_SUBJ_CLR                              = 0x11
	AUDIT_SUBJ_ROLE                             = 0xe
	AUDIT_SUBJ_SEN                              = 0x10
	AUDIT_SUBJ_TYPE                             = 0xf
	AUDIT_SUBJ_USER                             = 0xd
	AUDIT_SUCCESS                               = 0x68
	AUDIT_SUID                                  = 0x3
	AUDIT_SYSCALL                               = 0x514
	AUDIT_SYSCALL_CLASSES                       = 0x10
	AUDIT_TIME_ADJNTPVAL                        = 0x535
	AUDIT_TIME_INJOFFSET                        = 0x534
	AUDIT_TRIM                                  = 0x3f6
	AUDIT_TTY                                   = 0x527
	AUDIT_TTY_GET                               = 0x3f8
	AUDIT_TTY_SET                               = 0x3f9
	AUDIT_UID                                   = 0x1
	AUDIT_UID_UNSET                             = 0xffffffff
	AUDIT_UNUSED_BITS                           = 0x7fffc00
	AUDIT_URINGOP                               = 0x538
	AUDIT_USER                                  = 0x3ed
	AUDIT_USER_AVC                              = 0x453
	AUDIT_USER_TTY                              = 0x464
	AUDIT_VERSION_BACKLOG_LIMIT                 = 0x1
	AUDIT_VERSION_BACKLOG_WAIT_TIME             = 0x2
	AUDIT_VERSION_LATEST                        = 0x7f
	AUDIT_WATCH                                 = 0x69
	AUDIT_WATCH_INS                             = 0x3ef
	AUDIT_WATCH_LIST                            = 0x3f1
	AUDIT_WATCH_REM                             = 0x3f0
	AUTOFS_SUPER_MAGIC                          = 0x187
	B0                                          = 0x0
	B110                                        = 0x3
	B1200                                       = 0x9
	B134                                        = 0x4
	B150                                        = 0x5
	B1800                                       = 0xa
	B19200                                      = 0xe
	B200                                        = 0x6
	B2400                                       = 0xb
	B300                                        = 0x7
	B38400                                      = 0xf
	B4800                                       = 0xc
	B50                                         = 0x1
	B600                                        = 0x8
	B75                                         = 0x2
	B9600                                       = 0xd
	BDEVFS_MAGIC                                = 0x62646576
	BINDERFS_SUPER_MAGIC                        = 0x6c6f6f70
	BINFMTFS_MAGIC                              = 0x42494e4d
	BPF_A                                       = 0x10
	BPF_ABS                                     = 0x20
	BPF_ADD                                     = 0x0
	BPF_ALU                                     = 0x4
	BPF_ALU64                                   = 0x7
	BPF_AND                                     = 0x50
	BPF_ARSH                                    = 0xc0
	BPF_ATOMIC                                  = 0xc0
	BPF_B                                       = 0x10
	BPF_BUILD_ID_SIZE                           = 0x14
	BPF_CALL                                    = 0x80
	BPF_CMPXCHG                                 = 0xf1
	BPF_DIV                                     = 0x30
	BPF_DW                                      = 0x18
	BPF_END                                     = 0xd0
	BPF_EXIT                                    = 0x90
	BPF_FETCH                                   = 0x1
	BPF_FROM_BE                                 = 0x8
	BPF_FROM_LE                                 = 0x0
	BPF_FS_MAGIC                                = 0xcafe4a11
	BPF_F_AFTER                                 = 0x10
	BPF_F_ALLOW_MULTI                           = 0x2
	BPF_F_ALLOW_OVERRIDE                        = 0x1
	BPF_F_ANY_ALIGNMENT                         = 0x2
	BPF_F_BEFORE                                = 0x8
	BPF_F_ID                                    = 0x20
	BPF_F_NETFILTER_IP_DEFRAG                   = 0x1
	BPF_F_QUERY_EFFECTIVE                       = 0x1
	BPF_F_REPLACE                               = 0x4
	BPF_F_SLEEPABLE                             = 0x10
	BPF_F_STRICT_ALIGNMENT                      = 0x1
	BPF_F_TEST_REG_INVARIANTS                   = 0x80
	BPF_F_TEST_RND_HI32                         = 0x4
	BPF_F_TEST_RUN_ON_CPU                       = 0x1
	BPF_F_TEST_STATE_FREQ                       = 0x8
	BPF_F_TEST_XDP_LIVE_FRAMES                  = 0x2
	BPF_F_XDP_DEV_BOUND_ONLY                    = 0x40
	BPF_F_XDP_HAS_FRAGS                         = 0x20
	BPF_H                                       = 0x8
	BPF_IMM                                     = 0x0
	BPF_IND                                     = 0x40
	BPF_JA                                      = 0x0
	BPF_JEQ                                     = 0x10
	BPF_JGE                                     = 0x30
	BPF_JGT                                     = 0x20
	BPF_JLE                                     = 0xb0
	BPF_JLT                                     = 0xa0
	BPF_JMP                                     = 0x5
	BPF_JMP32                                   = 0x6
	BPF_JNE                                     = 0x50
	BPF_JSET                                    = 0x40
	BPF_JSGE                                    = 0x70
	BPF_JSGT                                    = 0x60
	BPF_JSLE                                    = 0xd0
	BPF_JSLT                                    = 0xc0
	BPF_K                                       = 0x0
	BPF_LD                                      = 0x0
	BPF_LDX                                     = 0x1
	BPF_LEN                                     = 0x80
	BPF_LL_OFF                                  = -0x200000
	BPF_LSH                                     = 0x60
	BPF_MAJOR_VERSION                           = 0x1
	BPF_MAXINSNS                                = 0x1000
	BPF_MEM                                     = 0x60
	BPF_MEMSX                                   = 0x80
	BPF_MEMWORDS                                = 0x10
	BPF_MINOR_VERSION                           = 0x1
	BPF_MISC                                    = 0x7
	BPF_MOD                                     = 0x90
	BPF_MOV                                     = 0xb0
	BPF_MSH                                     = 0xa0
	BPF_MUL                                     = 0x20
	BPF_NEG                                     = 0x80
	BPF_NET_OFF                                 = -0x100000
	BPF_OBJ_NAME_LEN                            = 0x10
	BPF_OR                                      = 0x40
	BPF_PSEUDO_BTF_ID                           = 0x3
	BPF_PSEUDO_CALL                             = 0x1
	BPF_PSEUDO_FUNC                             = 0x4
	BPF_PSEUDO_KFUNC_CALL                       = 0x2
	BPF_PSEUDO_MAP_FD                           = 0x1
	BPF_PSEUDO_MAP_IDX                          = 0x5
	BPF_PSEUDO_MAP_IDX_VALUE                    = 0x6
	BPF_PSEUDO_MAP_VALUE                        = 0x2
	BPF_RET                                     = 0x6
	BPF_RSH                                     = 0x70
	BPF_ST                                      = 0x2
	BPF_STX                                     = 0x3
	BPF_SUB                                     = 0x10
	BPF_TAG_SIZE                                = 0x8
	BPF_TAX                                     = 0x0
	BPF_TO_BE                                   = 0x8
	BPF_TO_LE                                   = 0x0
	BPF_TXA                                     = 0x80
	BPF_W                                       = 0x0
	BPF_X                                       = 0x8
	BPF_XADD                                    = 0xc0
	BPF_XCHG                                    = 0xe1
	BPF_XOR                                     = 0xa0
	BRKINT                                      = 0x2
	BS0                                         = 0x0
	BTRFS_SUPER_MAGIC                           = 0x9123683e
	BTRFS_TEST_MAGIC                            = 0x73727279
	BUS_BLUETOOTH                               = 0x5
	BUS_HIL                                     = 0x4
	BUS_USB                                     = 0x3
	BUS_VIRTUAL                                 = 0x6
	CAN_BCM                                     = 0x2
	CAN_BUS_OFF_THRESHOLD                       = 0x100
	CAN_CTRLMODE_3_SAMPLES                      = 0x4
	CAN_CTRLMODE_BERR_REPORTING                 = 0x10
	CAN_CTRLMODE_CC_LEN8_DLC                    = 0x100
	CAN_CTRLMODE_FD                             = 0x20
	CAN_CTRLMODE_FD_NON_ISO                     = 0x80
	CAN_CTRLMODE_LISTENONLY                     = 0x2
	CAN_CTRLMODE_LOOPBACK                       = 0x1
	CAN_CTRLMODE_ONE_SHOT                       = 0x8
	CAN_CTRLMODE_PRESUME_ACK                    = 0x40
	CAN_CTRLMODE_TDC_AUTO                       = 0x200
	CAN_CTRLMODE_TDC_MANUAL                     = 0x400
	CAN_EFF_FLAG                                = 0x80000000
	CAN_EFF_ID_BITS                             = 0x1d
	CAN_EFF_MASK                                = 0x1fffffff
	CAN_ERROR_PASSIVE_THRESHOLD                 = 0x80
	CAN_ERROR_WARNING_THRESHOLD                 = 0x60
	CAN_ERR_ACK                                 = 0x20
	CAN_ERR_BUSERROR                            = 0x80
	CAN_ERR_BUSOFF                              = 0x40
	CAN_ERR_CNT                                 = 0x200
	CAN_ERR_CRTL                                = 0x4
	CAN_ERR_CRTL_ACTIVE                         = 0x40
	CAN_ERR_CRTL_RX_OVERFLOW                    = 0x1
	CAN_ERR_CRTL_RX_PASSIVE                     = 0x10
	CAN_ERR_CRTL_RX_WARNING                     = 0x4
	CAN_ERR_CRTL_TX_OVERFLOW                    = 0x2
	CAN_ERR_CRTL_TX_PASSIVE                     = 0x20
	CAN_ERR_CRTL_TX_WARNING                     = 0x8
	CAN_ERR_CRTL_UNSPEC                         = 0x0
	CAN_ERR_DLC                                 = 0x8
	CAN_ERR_FLAG                                = 0x20000000
	CAN_ERR_LOSTARB                             = 0x2
	CAN_ERR_LOSTARB_UNSPEC                      = 0x0
	CAN_ERR_MASK                                = 0x1fffffff
	CAN_ERR_PROT                                = 0x8
	CAN_ERR_PROT_ACTIVE                         = 0x40
	CAN_ERR_PROT_BIT                            = 0x1
	CAN_ERR_PROT_BIT0                           = 0x8
	CAN_ERR_PROT_BIT1                           = 0x10
	CAN_ERR_PROT_FORM                           = 0x2
	CAN_ERR_PROT_LOC_ACK                        = 0x19
	CAN_ERR_PROT_LOC_ACK_DEL                    = 0x1b
	CAN_ERR_PROT_LOC_CRC_DEL                    = 0x18
	CAN_ERR_PROT_LOC_CRC_SEQ                    = 0x8
	CAN_ERR_PROT_LOC_DATA                       = 0xa
	CAN_ERR_PROT_LOC_DLC                        = 0xb
	CAN_ERR_PROT_LOC_EOF                        = 0x1a
	CAN_ERR_PROT_LOC_ID04_00                    = 0xe
	CAN_ERR_PROT_LOC_ID12_05                    = 0xf
	CAN_ERR_PROT_LOC_ID17_13                    = 0x7
	CAN_ERR_PROT_LOC_ID20_18                    = 0x6
	CAN_ERR_PROT_LOC_ID28_21                    = 0x2
	CAN_ERR_PROT_LOC_IDE                        = 0x5
	CAN_ERR_PROT_LOC_INTERM                     = 0x12
	CAN_ERR_PROT_LOC_RES0                       = 0x9
	CAN_ERR_PROT_LOC_RES1                       = 0xd
	CAN_ERR_PROT_LOC_RTR                        = 0xc
	CAN_ERR_PROT_LOC_SOF                        = 0x3
	CAN_ERR_PROT_LOC_SRTR                       = 0x4
	CAN_ERR_PROT_LOC_UNSPEC                     = 0x0
	CAN_ERR_PROT_OVERLOAD                       = 0x20
	CAN_ERR_PROT_STUFF                          = 0x4
	CAN_ERR_PROT_TX                             = 0x80
	CAN_ERR_PROT_UNSPEC                         = 0x0
	CAN_ERR_RESTARTED                           = 0x100
	CAN_ERR_TRX                                 = 0x10
	CAN_ERR_TRX_CANH_NO_WIRE                    = 0x4
	CAN_ERR_TRX_CANH_SHORT_TO_BAT               = 0x5
	CAN_ERR_TRX_CANH_SHORT_TO_GND               = 0x7
	CAN_ERR_TRX_CANH_SHORT_TO_VCC               = 0x6
	CAN_ERR_TRX_CANL_NO_WIRE                    = 0x40
	CAN_ERR_TRX_CANL_SHORT_TO_BAT               = 0x50
	CAN_ERR_TRX_CANL_SHORT_TO_CANH              = 0x80
	CAN_ERR_TRX_CANL_SHORT_TO_GND               = 0x70
	CAN_ERR_TRX_CANL_SHORT_TO_VCC               = 0x60
	CAN_ERR_TRX_UNSPEC                          = 0x0
	CAN_ERR_TX_TIMEOUT                          = 0x1
	CAN_INV_FILTER                              = 0x20000000
	CAN_ISOTP                                   = 0x6
	CAN_J1939                                   = 0x7
	CAN_MAX_DLC                                 = 0x8
	CAN_MAX_DLEN                                = 0x8
	CAN_MAX_RAW_DLC                             = 0xf
	CAN_MCNET                                   = 0x5
	CAN_MTU                                     = 0x10
	CAN_NPROTO                                  = 0x8
	CAN_RAW                                     = 0x1
	CAN_RAW_FILTER_MAX                          = 0x200
	CAN_RTR_FLAG                                = 0x40000000
	CAN_SFF_ID_BITS                             = 0xb
	CAN_SFF_MASK                                = 0x7ff
	CAN_TERMINATION_DISABLED                    = 0x0
	CAN_TP16                                    = 0x3
	CAN_TP20                                    = 0x4
	CAP_AUDIT_CONTROL                           = 0x1e
	CAP_AUDIT_READ                              = 0x25
	CAP_AUDIT_WRITE                             = 0x1d
	CAP_BLOCK_SUSPEND                           = 0x24
	CAP_BPF                                     = 0x27
	CAP_CHECKPOINT_RESTORE                      = 0x28
	CAP_CHOWN                                   = 0x0
	CAP_DAC_OVERRIDE                            = 0x1
	CAP_DAC_READ_SEARCH                         = 0x2
	CAP_FOWNER                                  = 0x3
	CAP_FSETID                                  = 0x4
	CAP_IPC_LOCK                                = 0xe
	CAP_IPC_OWNER                               = 0xf
	CAP_KILL                                    = 0x5
	CAP_LAST_CAP                                = 0x28
	CAP_LEASE                                   = 0x1c
	CAP_LINUX_IMMUTABLE                         = 0x9
	CAP_MAC_ADMIN                               = 0x21
	CAP_MAC_OVERRIDE                            = 0x20
	CAP_MKNOD                                   = 0x1b
	CAP_NET_ADMIN                               = 0xc
	CAP_NET_BIND_SERVICE                        = 0xa
	CAP_NET_BROADCAST                           = 0xb
	CAP_NET_RAW                                 = 0xd
	CAP_PERFMON                                 = 0x26
	CAP_SETFCAP                                 = 0x1f
	CAP_SETGID                                  = 0x6
	CAP_SETPCAP                                 = 0x8
	CAP_SETUID                                  = 0x7
	CAP_SYSLOG                                  = 0x22
	CAP_SYS_ADMIN                               = 0x15
	CAP_SYS_BOOT                                = 0x16
	CAP_SYS_CHROOT                              = 0x12
	CAP_SYS_MODULE                              = 0x10
	CAP_SYS_NICE                                = 0x17
	CAP_SYS_PACCT                               = 0x14
	CAP_SYS_PTRACE                              = 0x13
	CAP_SYS_RAWIO                               = 0x11
	CAP_SYS_RESOURCE                            = 0x18
	CAP_SYS_TIME                                = 0x19
	CAP_SYS_TTY_CONFIG                          = 0x1a
	CAP_WAKE_ALARM                              = 0x23
	CEPH_SUPER_MAGIC                            = 0xc36400
	CFLUSH                                      = 0xf
	CGROUP2_SUPER_MAGIC                         = 0x63677270
	CGROUP_SUPER_MAGIC                          = 0x27e0eb
	CIFS_SUPER_MAGIC                            = 0xff534d42
	CLOCK_BOOTTIME                              = 0x7
	CLOCK_BOOTTIME_ALARM                        = 0x9
	CLOCK_DEFAULT                               = 0x0
	CLOCK_EXT                                   = 0x1
	CLOCK_INT                                   = 0x2
	CLOCK_MONOTONIC                             = 0x1
	CLOCK_MONOTONIC_COARSE                      = 0x6
	CLOCK_MONOTONIC_RAW                         = 0x4
	CLOCK_PROCESS_CPUTIME_ID                    = 0x2
	CLOCK_REALTIME                              = 0x0
	CLOCK_REALTIME_ALARM                        = 0x8
	CLOCK_REALTIME_COARSE                       = 0x5
	CLOCK_TAI                                   = 0xb
	CLOCK_THREAD_CPUTIME_ID                     = 0x3
	CLOCK_TXFROMRX                              = 0x4
	CLOCK_TXINT                                 = 0x3
	CLONE_ARGS_SIZE_VER0                        = 0x40
	CLONE_ARGS_SIZE_VER1                        = 0x50
	CLONE_ARGS_SIZE_VER2                        = 0x58
	CLONE_CHILD_CLEARTID                        = 0x200000
	CLONE_CHILD_SETTID                          = 0x1000000
	CLONE_CLEAR_SIGHAND                         = 0x100000000
	CLONE_DETACHED                              = 0x400000
	CLONE_FILES                                 = 0x400
	CLONE_FS                                    = 0x200
	CLONE_INTO_CGROUP                           = 0x200000000
	CLONE_IO                                    = 0x80000000
	CLONE_NEWCGROUP                             = 0x2000000
	CLONE_NEWIPC                                = 0x8000000
	CLONE_NEWNET                                = 0x40000000
	CLONE_NEWNS                                 = 0x20000
	CLONE_NEWPID                                = 0x20000000
	CLONE_NEWTIME                               = 0x80
	CLONE_NEWUSER                               = 0x10000000
	CLONE_NEWUTS                                = 0x4000000
	CLONE_PARENT                                = 0x8000
	CLONE_PARENT_SETTID                         = 0x100000
	CLONE_PIDFD                                 = 0x1000
	CLONE_PTRACE                                = 0x2000
	CLONE_SETTLS                                = 0x80000
	CLONE_SIGHAND                               = 0x800
	CLONE_SYSVSEM                               = 0x40000
	CLONE_THREAD                                = 0x10000
	CLONE_UNTRACED                              = 0x800000
	CLONE_VFORK                                 = 0x4000
	CLONE_VM                                    = 0x100
	CMSPAR                                      = 0x40000000
	CODA_SUPER_MAGIC                            = 0x73757245
	CR0                                         = 0x0
	CRAMFS_MAGIC                                = 0x28cd3d45
	CRTSCTS                                     = 0x80000000
	CRYPTO_MAX_NAME                             = 0x40
	CRYPTO_MSG_MAX                              = 0x15
	CRYPTO_NR_MSGTYPES                          = 0x6
	CRYPTO_REPORT_MAXSIZE                       = 0x160
	CS5                                         = 0x0
	CSIGNAL                                     = 0xff
	CSTART                                      = 0x11
	CSTATUS                                     = 0x0
	CSTOP                                       = 0x13
	CSUSP                                       = 0x1a
	DAXFS_MAGIC                                 = 0x64646178
	DEBUGFS_MAGIC                               = 0x64626720
	DEVLINK_CMD_ESWITCH_MODE_GET                = 0x1d
	DEVLINK_CMD_ESWITCH_MODE_SET                = 0x1e
	DEVLINK_FLASH_OVERWRITE_IDENTIFIERS         = 0x2
	DEVLINK_FLASH_OVERWRITE_SETTINGS            = 0x1
	DEVLINK_GENL_MCGRP_CONFIG_NAME              = "config"
	DEVLINK_GENL_NAME                           = "devlink"
	DEVLINK_GENL_VERSION                        = 0x1
	DEVLINK_PORT_FN_CAP_IPSEC_CRYPTO            = 0x4
	DEVLINK_PORT_FN_CAP_IPSEC_PACKET            = 0x8
	DEVLINK_PORT_FN_CAP_MIGRATABLE              = 0x2
	DEVLINK_PORT_FN_CAP_ROCE                    = 0x1
	DEVLINK_SB_THRESHOLD_TO_ALPHA_MAX           = 0x14
	DEVLINK_SUPPORTED_FLASH_OVERWRITE_SECTIONS  = 0x3
	DEVMEM_MAGIC                                = 0x454d444d
	DEVPTS_SUPER_MAGIC                          = 0x1cd1
	DMA_BUF_MAGIC                               = 0x444d4142
	DM_ACTIVE_PRESENT_FLAG                      = 0x20
	DM_BUFFER_FULL_FLAG                         = 0x100
	DM_CONTROL_NODE                             = "control"
	DM_DATA_OUT_FLAG                            = 0x10000
	DM_DEFERRED_REMOVE                          = 0x20000
	DM_DEV_ARM_POLL                             = 0xc138fd10
	DM_DEV_CREATE                               = 0xc138fd03
	DM_DEV_REMOVE                               = 0xc138fd04
	DM_DEV_RENAME                               = 0xc138fd05
	DM_DEV_SET_GEOMETRY                         = 0xc138fd0f
	DM_DEV_STATUS                               = 0xc138fd07
	DM_DEV_SUSPEND                              = 0xc138fd06
	DM_DEV_WAIT                                 = 0xc138fd08
	DM_DIR                                      = "mapper"
	DM_GET_TARGET_VERSION                       = 0xc138fd11
	DM_IMA_MEASUREMENT_FLAG                     = 0x80000
	DM_INACTIVE_PRESENT_FLAG                    = 0x40
	DM_INTERNAL_SUSPEND_FLAG                    = 0x40000
	DM_IOCTL                                    = 0xfd
	DM_LIST_DEVICES                             = 0xc138fd02
	DM_LIST_VERSIONS                            = 0xc138fd0d
	DM_MAX_TYPE_NAME                            = 0x10
	DM_NAME_LEN                                 = 0x80
	DM_NAME_LIST_FLAG_DOESNT_HAVE_UUID          = 0x2
	DM_NAME_LIST_FLAG_HAS_UUID                  = 0x1
	DM_NOFLUSH_FLAG                             = 0x800
	DM_PERSISTENT_DEV_FLAG                      = 0x8
	DM_QUERY_INACTIVE_TABLE_FLAG                = 0x1000
	DM_READONLY_FLAG                            = 0x1
	DM_REMOVE_ALL                               = 0xc138fd01
	DM_SECURE_DATA_FLAG                         = 0x8000
	DM_SKIP_BDGET_FLAG                          = 0x200
	DM_SKIP_LOCKFS_FLAG                         = 0x400
	DM_STATUS_TABLE_FLAG                        = 0x10
	DM_SUSPEND_FLAG                             = 0x2
	DM_TABLE_CLEAR                              = 0xc138fd0a
	DM_TABLE_DEPS                               = 0xc138fd0b
	DM_TABLE_LOAD                               = 0xc138fd09
	DM_TABLE_STATUS                             = 0xc138fd0c
	DM_TARGET_MSG                               = 0xc138fd0e
	DM_UEVENT_GENERATED_FLAG                    = 0x2000
	DM_UUID_FLAG                                = 0x4000
	DM_UUID_LEN                                 = 0x81
	DM_VERSION                                  = 0xc138fd00
	DM_VERSION_EXTRA                            = "-ioctl (2023-03-01)"
	DM_VERSION_MAJOR                            = 0x4
	DM_VERSION_MINOR                            = 0x30
	DM_VERSION_PATCHLEVEL                       = 0x0
	DT_BLK                                      = 0x6
	DT_CHR                                      = 0x2
	DT_DIR                                      = 0x4
	DT_FIFO                                     = 0x1
	DT_LNK                                      = 0xa
	DT_REG                                      = 0x8
	DT_SOCK                                     = 0xc
	DT_UNKNOWN                                  = 0x0
	DT_WHT                                      = 0xe
	ECHO                                        = 0x8
	ECRYPTFS_SUPER_MAGIC                        = 0xf15f
	EFD_SEMAPHORE                               = 0x1
	EFIVARFS_MAGIC                              = 0xde5e81e4
	EFS_SUPER_MAGIC                             = 0x414a53
	EM_386                                      = 0x3
	EM_486                                      = 0x6
	EM_68K                                      = 0x4
	EM_860                                      = 0x7
	EM_88K                                      = 0x5
	EM_AARCH64                                  = 0xb7
	EM_ALPHA                                    = 0x9026
	EM_ALTERA_NIOS2                             = 0x71
	EM_ARCOMPACT                                = 0x5d
	EM_ARCV2                                    = 0xc3
	EM_ARM                                      = 0x28
	EM_BLACKFIN                                 = 0x6a
	EM_BPF                                      = 0xf7
	EM_CRIS                                     = 0x4c
	EM_CSKY                                     = 0xfc
	EM_CYGNUS_M32R                              = 0x9041
	EM_CYGNUS_MN10300                           = 0xbeef
	EM_FRV                                      = 0x5441
	EM_H8_300                                   = 0x2e
	EM_HEXAGON                                  = 0xa4
	EM_IA_64                                    = 0x32
	EM_LOONGARCH                                = 0x102
	EM_M32                                      = 0x1
	EM_M32R                                     = 0x58
	EM_MICROBLAZE                               = 0xbd
	EM_MIPS                                     = 0x8
	EM_MIPS_RS3_LE                              = 0xa
	EM_MIPS_RS4_BE                              = 0xa
	EM_MN10300                                  = 0x59
	EM_NDS32                                    = 0xa7
	EM_NONE                                     = 0x0
	EM_OPENRISC                                 = 0x5c
	EM_PARISC                                   = 0xf
	EM_PPC                                      = 0x14
	EM_PPC64                                    = 0x15
	EM_RISCV                                    = 0xf3
	EM_S390                                     = 0x16
	EM_S390_OLD                                 = 0xa390
	EM_SH                                       = 0x2a
	EM_SPARC                                    = 0x2
	EM_SPARC32PLUS                              = 0x12
	EM_SPARCV9                                  = 0x2b
	EM_SPU                                      = 0x17
	EM_TILEGX                                   = 0xbf
	EM_TILEPRO                                  = 0xbc
	EM_TI_C6000                                 = 0x8c
	EM_UNICORE                                  = 0x6e
	EM_X86_64                                   = 0x3e
	EM_XTENSA                                   = 0x5e
	ENCODING_DEFAULT                            = 0x0
	ENCODING_FM_MARK                            = 0x3
	ENCODING_FM_SPACE                           = 0x4
	ENCODING_MANCHESTER                         = 0x5
	ENCODING_NRZ                                = 0x1
	ENCODING_NRZI                               = 0x2
	EPOLLERR                                    = 0x8
	EPOLLET                                     = 0x80000000
	EPOLLEXCLUSIVE                              = 0x10000000
	EPOLLHUP                                    = 0x10
	EPOLLIN                                     = 0x1
	EPOLLMSG                                    = 0x400
	EPOLLONESHOT                                = 0x40000000
	EPOLLOUT                                    = 0x4
	EPOLLPRI                                    = 0x2
	EPOLLRDBAND                                 = 0x80
	EPOLLRDHUP                                  = 0x2000
	EPOLLRDNORM                                 = 0x40
	EPOLLWAKEUP                                 = 0x20000000
	EPOLLWRBAND                                 = 0x200
	EPOLLWRNORM                                 = 0x100
	EPOLL_CTL_ADD                               = 0x1
	EPOLL_CTL_DEL                               = 0x2
	EPOLL_CTL_MOD                               = 0x3
	EROFS_SUPER_MAGIC_V1                        = 0xe0f5e1e2
	ESP_V4_FLOW                                 = 0xa
	ESP_V6_FLOW                                 = 0xc
	ETHER_FLOW                                  = 0x12
	ETHTOOL_BUSINFO_LEN                         = 0x20
	ETHTOOL_EROMVERS_LEN                        = 0x20
	ETHTOOL_FEC_AUTO                            = 0x2
	ETHTOOL_FEC_BASER                           = 0x10
	ETHTOOL_FEC_LLRS                            = 0x20
	ETHTOOL_FEC_NONE                            = 0x1
	ETHTOOL_FEC_OFF                             = 0x4
	ETHTOOL_FEC_RS                              = 0x8
	ETHTOOL_FLAG_ALL                            = 0x7
	ETHTOOL_FLAG_COMPACT_BITSETS                = 0x1
	ETHTOOL_FLAG_OMIT_REPLY                     = 0x2
	ETHTOOL_FLAG_STATS                          = 0x4
	ETHTOOL_FLASHDEV                            = 0x33
	ETHTOOL_FLASH_MAX_FILENAME                  = 0x80
	ETHTOOL_FWVERS_LEN                          = 0x20
	ETHTOOL_F_COMPAT                            = 0x4
	ETHTOOL_F_UNSUPPORTED                       = 0x1
	ETHTOOL_F_WISH                              = 0x2
	ETHTOOL_GCHANNELS                           = 0x3c
	ETHTOOL_GCOALESCE                           = 0xe
	ETHTOOL_GDRVINFO                            = 0x3
	ETHTOOL_GEEE                                = 0x44
	ETHTOOL_GEEPROM                             = 0xb
	ETHTOOL_GENL_NAME                           = "ethtool"
	ETHTOOL_GENL_VERSION                        = 0x1
	ETHTOOL_GET_DUMP_DATA                       = 0x40
	ETHTOOL_GET_DUMP_FLAG                       = 0x3f
	ETHTOOL_GET_TS_INFO                         = 0x41
	ETHTOOL_GFEATURES                           = 0x3a
	ETHTOOL_GFECPARAM                           = 0x50
	ETHTOOL_GFLAGS                              = 0x25
	ETHTOOL_GGRO                                = 0x2b
	ETHTOOL_GGSO                                = 0x23
	ETHTOOL_GLINK                               = 0xa
	ETHTOOL_GLINKSETTINGS                       = 0x4c
	ETHTOOL_GMODULEEEPROM                       = 0x43
	ETHTOOL_GMODULEINFO                         = 0x42
	ETHTOOL_GMSGLVL                             = 0x7
	ETHTOOL_GPAUSEPARAM                         = 0x12
	ETHTOOL_GPERMADDR                           = 0x20
	ETHTOOL_GPFLAGS                             = 0x27
	ETHTOOL_GPHYSTATS                           = 0x4a
	ETHTOOL_GREGS                               = 0x4
	ETHTOOL_GRINGPARAM                          = 0x10
	ETHTOOL_GRSSH                               = 0x46
	ETHTOOL_GRXCLSRLALL                         = 0x30
	ETHTOOL_GRXCLSRLCNT                         = 0x2e
	ETHTOOL_GRXCLSRULE                          = 0x2f
	ETHTOOL_GRXCSUM                             = 0x14
	ETHTOOL_GRXFH                               = 0x29
	ETHTOOL_GRXFHINDIR                          = 0x38
	ETHTOOL_GRXNTUPLE                           = 0x36
	ETHTOOL_GRXRINGS                            = 0x2d
	ETHTOOL_GSET                                = 0x1
	ETHTOOL_GSG                                 = 0x18
	ETHTOOL_GSSET_INFO                          = 0x37
	ETHTOOL_GSTATS                              = 0x1d
	ETHTOOL_GSTRINGS                            = 0x1b
	ETHTOOL_GTSO                                = 0x1e
	ETHTOOL_GTUNABLE                            = 0x48
	ETHTOOL_GTXCSUM                             = 0x16
	ETHTOOL_GUFO                                = 0x21
	ETHTOOL_GWOL                                = 0x5
	ETHTOOL_MCGRP_MONITOR_NAME                  = "monitor"
	ETHTOOL_NWAY_RST                            = 0x9
	ETHTOOL_PERQUEUE                            = 0x4b
	ETHTOOL_PHYS_ID                             = 0x1c
	ETHTOOL_PHY_EDPD_DFLT_TX_MSECS              = 0xffff
	ETHTOOL_PHY_EDPD_DISABLE                    = 0x0
	ETHTOOL_PHY_EDPD_NO_TX                      = 0xfffe
	ETHTOOL_PHY_FAST_LINK_DOWN_OFF              = 0xff
	ETHTOOL_PHY_FAST_LINK_DOWN_ON               = 0x0
	ETHTOOL_PHY_GTUNABLE                        = 0x4e
	ETHTOOL_PHY_STUNABLE                        = 0x4f
	ETHTOOL_RESET                               = 0x34
	ETHTOOL_RXNTUPLE_ACTION_CLEAR               = -0x2
	ETHTOOL_RXNTUPLE_ACTION_DROP                = -0x1
	ETHTOOL_RX_FLOW_SPEC_RING                   = 0xffffffff
	ETHTOOL_RX_FLOW_SPEC_RING_VF                = 0xff00000000
	ETHTOOL_RX_FLOW_SPEC_RING_VF_OFF            = 0x20
	ETHTOOL_SCHANNELS                           = 0x3d
	ETHTOOL_SCOALESCE                           = 0xf
	ETHTOOL_SEEE                                = 0x45
	ETHTOOL_SEEPROM                             = 0xc
	ETHTOOL_SET_DUMP                            = 0x3e
	ETHTOOL_SFEATURES                           = 0x3b
	ETHTOOL_SFECPARAM                           = 0x51
	ETHTOOL_SFLAGS                              = 0x26
	ETHTOOL_SGRO                                = 0x2c
	ETHTOOL_SGSO                                = 0x24
	ETHTOOL_SLINKSETTINGS                       = 0x4d
	ETHTOOL_SMSGLVL                             = 0x8
	ETHTOOL_SPAUSEPARAM                         = 0x13
	ETHTOOL_SPFLAGS                             = 0x28
	ETHTOOL_SRINGPARAM                          = 0x11
	ETHTOOL_SRSSH                               = 0x47
	ETHTOOL_SRXCLSRLDEL                         = 0x31
	ETHTOOL_SRXCLSRLINS                         = 0x32
	ETHTOOL_SRXCSUM                             = 0x15
	ETHTOOL_SRXFH                               = 0x2a
	ETHTOOL_SRXFHINDIR                          = 0x39
	ETHTOOL_SRXNTUPLE                           = 0x35
	ETHTOOL_SSET                                = 0x2
	ETHTOOL_SSG                                 = 0x19
	ETHTOOL_STSO                                = 0x1f
	ETHTOOL_STUNABLE                            = 0x49
	ETHTOOL_STXCSUM                             = 0x17
	ETHTOOL_SUFO                                = 0x22
	ETHTOOL_SWOL                                = 0x6
	ETHTOOL_TEST                                = 0x1a
	ETH_P_1588                                  = 0x88f7
	ETH_P_8021AD                                = 0x88a8
	ETH_P_8021AH                                = 0x88e7
	ETH_P_8021Q                                 = 0x8100
	ETH_P_80221                                 = 0x8917
	ETH_P_802_2                                 = 0x4
	ETH_P_802_3                                 = 0x1
	ETH_P_802_3_MIN                             = 0x600
	ETH_P_802_EX1                               = 0x88b5
	ETH_P_AARP                                  = 0x80f3
	ETH_P_AF_IUCV                               = 0xfbfb
	ETH_P_ALL                                   = 0x3
	ETH_P_AOE                                   = 0x88a2
	ETH_P_ARCNET                                = 0x1a
	ETH_P_ARP                                   = 0x806
	ETH_P_ATALK                                 = 0x809b
	ETH_P_ATMFATE                               = 0x8884
	ETH_P_ATMMPOA                               = 0x884c
	ETH_P_AX25                                  = 0x2
	ETH_P_BATMAN                                = 0x4305
	ETH_P_BPQ                                   = 0x8ff
	ETH_P_CAIF                                  = 0xf7
	ETH_P_CAN                                   = 0xc
	ETH_P_CANFD                                 = 0xd
	ETH_P_CANXL                                 = 0xe
	ETH_P_CFM                                   = 0x8902
	ETH_P_CONTROL                               = 0x16
	ETH_P_CUST                                  = 0x6006
	ETH_P_DDCMP                                 = 0x6
	ETH_P_DEC                                   = 0x6000
	ETH_P_DIAG                                  = 0x6005
	ETH_P_DNA_DL                                = 0x6001
	ETH_P_DNA_RC                                = 0x6002
	ETH_P_DNA_RT                                = 0x6003
	ETH_P_DSA                                   = 0x1b
	ETH_P_DSA_8021Q                             = 0xdadb
	ETH_P_DSA_A5PSW                             = 0xe001
	ETH_P_ECONET                                = 0x18
	ETH_P_EDSA                                  = 0xdada
	ETH_P_ERSPAN                                = 0x88be
	ETH_P_ERSPAN2                               = 0x22eb
	ETH_P_ETHERCAT                              = 0x88a4
	ETH_P_FCOE                                  = 0x8906
	ETH_P_FIP                                   = 0x8914
	ETH_P_HDLC                                  = 0x19
	ETH_P_HSR                                   = 0x892f
	ETH_P_IBOE                                  = 0x8915
	ETH_P_IEEE802154                            = 0xf6
	ETH_P_IEEEPUP                               = 0xa00
	ETH_P_IEEEPUPAT                             = 0xa01
	ETH_P_IFE                                   = 0xed3e
	ETH_P_IP                                    = 0x800
	ETH_P_IPV6                                  = 0x86dd
	ETH_P_IPX                                   = 0x8137
	ETH_P_IRDA                                  = 0x17
	ETH_P_LAT                                   = 0x6004
	ETH_P_LINK_CTL                              = 0x886c
	ETH_P_LLDP                                  = 0x88cc
	ETH_P_LOCALTALK                             = 0x9
	ETH_P_LOOP                                  = 0x60
	ETH_P_LOOPBACK                              = 0x9000
	ETH_P_MACSEC                                = 0x88e5
	ETH_P_MAP                                   = 0xf9
	ETH_P_MCTP                                  = 0xfa
	ETH_P_MOBITEX                               = 0x15
	ETH_P_MPLS_MC                               = 0x8848
	ETH_P_MPLS_UC                               = 0x8847
	ETH_P_MRP                                   = 0x88e3
	ETH_P_MVRP                                  = 0x88f5
	ETH_P_NCSI                                  = 0x88f8
	ETH_P_NSH                                   = 0x894f
	ETH_P_PAE                                   = 0x888e
	ETH_P_PAUSE                                 = 0x8808
	ETH_P_PHONET                                = 0xf5
	ETH_P_PPPTALK                               = 0x10
	ETH_P_PPP_DISC                              = 0x8863
	ETH_P_PPP_MP                                = 0x8
	ETH_P_PPP_SES                               = 0x8864
	ETH_P_PREAUTH                               = 0x88c7
	ETH_P_PROFINET                              = 0x8892
	ETH_P_PRP                                   = 0x88fb
	ETH_P_PUP                                   = 0x200
	ETH_P_PUPAT                                 = 0x201
	ETH_P_QINQ1                                 = 0x9100
	ETH_P_QINQ2                                 = 0x9200
	ETH_P_QINQ3                                 = 0x9300
	ETH_P_RARP                                  = 0x8035
	ETH_P_REALTEK                               = 0x8899
	ETH_P_SCA                                   = 0x6007
	ETH_P_SLOW                                  = 0x8809
	ETH_P_SNAP                                  = 0x5
	ETH_P_TDLS                                  = 0x890d
	ETH_P_TEB                                   = 0x6558
	ETH_P_TIPC                                  = 0x88ca
	ETH_P_TRAILER                               = 0x1c
	ETH_P_TR_802_2                              = 0x11
	ETH_P_TSN                                   = 0x22f0
	ETH_P_WAN_PPP                               = 0x7
	ETH_P_WCCP                                  = 0x883e
	ETH_P_X25                                   = 0x805
	ETH_P_XDSA                                  = 0xf8
	EV_ABS                                      = 0x3
	EV_CNT                                      = 0x20
	EV_FF                                       = 0x15
	EV_FF_STATUS                                = 0x17
	EV_KEY                                      = 0x1
	EV_LED                                      = 0x11
	EV_MAX                                      = 0x1f
	EV_MSC                                      = 0x4
	EV_PWR                                      = 0x16
	EV_REL                                      = 0x2
	EV_REP                                      = 0x14
	EV_SND                                      = 0x12
	EV_SW                                       = 0x5
	EV_SYN                                      = 0x0
	EV_VERSION                                  = 0x10001
	EXABYTE_ENABLE_NEST                         = 0xf0
	EXFAT_SUPER_MAGIC                           = 0x2011bab0
	EXT2_SUPER_MAGIC                            = 0xef53
	EXT3_SUPER_MAGIC                            = 0xef53
	EXT4_SUPER_MAGIC                            = 0xef53
	EXTA                                        = 0xe
	EXTB                                        = 0xf
	F2FS_SUPER_MAGIC                            = 0xf2f52010
	FALLOC_FL_COLLAPSE_RANGE                    = 0x8
	FALLOC_FL_INSERT_RANGE                      = 0x20
	FALLOC_FL_KEEP_SIZE                         = 0x1
	FALLOC_FL_NO_HIDE_STALE                     = 0x4
	FALLOC_FL_PUNCH_HOLE                        = 0x2
	FALLOC_FL_UNSHARE_RANGE                     = 0x40
	FALLOC_FL_ZERO_RANGE                        = 0x10
	FANOTIFY_METADATA_VERSION                   = 0x3
	FAN_ACCESS                                  = 0x1
	FAN_ACCESS_PERM                             = 0x20000
	FAN_ALLOW                                   = 0x1
	FAN_ALL_CLASS_BITS                          = 0xc
	FAN_ALL_EVENTS                              = 0x3b
	FAN_ALL_INIT_FLAGS                          = 0x3f
	FAN_ALL_MARK_FLAGS                          = 0xff
	FAN_ALL_OUTGOING_EVENTS                     = 0x3403b
	FAN_ALL_PERM_EVENTS                         = 0x30000
	FAN_ATTRIB                                  = 0x4
	FAN_AUDIT                                   = 0x10
	FAN_CLASS_CONTENT                           = 0x4
	FAN_CLASS_NOTIF                             = 0x0
	FAN_CLASS_PRE_CONTENT                       = 0x8
	FAN_CLOEXEC                                 = 0x1
	FAN_CLOSE                                   = 0x18
	FAN_CLOSE_NOWRITE                           = 0x10
	FAN_CLOSE_WRITE                             = 0x8
	FAN_CREATE                                  = 0x100
	FAN_DELETE                                  = 0x200
	FAN_DELETE_SELF                             = 0x400
	FAN_DENY                                    = 0x2
	FAN_ENABLE_AUDIT                            = 0x40
	FAN_EPIDFD                                  = -0x2
	FAN_EVENT_INFO_TYPE_DFID                    = 0x3
	FAN_EVENT_INFO_TYPE_DFID_NAME               = 0x2
	FAN_EVENT_INFO_TYPE_ERROR                   = 0x5
	FAN_EVENT_INFO_TYPE_FID                     = 0x1
	FAN_EVENT_INFO_TYPE_NEW_DFID_NAME           = 0xc
	FAN_EVENT_INFO_TYPE_OLD_DFID_NAME           = 0xa
	FAN_EVENT_INFO_TYPE_PIDFD                   = 0x4
	FAN_EVENT_METADATA_LEN                      = 0x18
	FAN_EVENT_ON_CHILD                          = 0x8000000
	FAN_FS_ERROR                                = 0x8000
	FAN_INFO                                    = 0x20
	FAN_MARK_ADD                                = 0x1
	FAN_MARK_DONT_FOLLOW                        = 0x4
	FAN_MARK_EVICTABLE                          = 0x200
	FAN_MARK_FILESYSTEM                         = 0x100
	FAN_MARK_FLUSH                              = 0x80
	FAN_MARK_IGNORE                             = 0x400
	FAN_MARK_IGNORED_MASK                       = 0x20
	FAN_MARK_IGNORED_SURV_MODIFY                = 0x40
	FAN_MARK_IGNORE_SURV                        = 0x440
	FAN_MARK_INODE                              = 0x0
	FAN_MARK_MOUNT                              = 0x10
	FAN_MARK_ONLYDIR                            = 0x8
	FAN_MARK_REMOVE                             = 0x2
	FAN_MODIFY                                  = 0x2
	FAN_MOVE                                    = 0xc0
	FAN_MOVED_FROM                              = 0x40
	FAN_MOVED_TO                                = 0x80
	FAN_MOVE_SELF                               = 0x800
	FAN_NOFD                                    = -0x1
	FAN_NONBLOCK                                = 0x2
	FAN_NOPIDFD                                 = -0x1
	FAN_ONDIR                                   = 0x40000000
	FAN_OPEN                                    = 0x20
	FAN_OPEN_EXEC                               = 0x1000
	FAN_OPEN_EXEC_PERM                          = 0x40000
	FAN_OPEN_PERM                               = 0x10000
	FAN_Q_OVERFLOW                              = 0x4000
	FAN_RENAME                                  = 0x10000000
	FAN_REPORT_DFID_NAME                        = 0xc00
	FAN_REPORT_DFID_NAME_TARGET                 = 0x1e00
	FAN_REPORT_DIR_FID                          = 0x400
	FAN_REPORT_FID                              = 0x200
	FAN_REPORT_NAME                             = 0x800
	FAN_REPORT_PIDFD                            = 0x80
	FAN_REPORT_TARGET_FID                       = 0x1000
	FAN_REPORT_TID                              = 0x100
	FAN_RESPONSE_INFO_AUDIT_RULE                = 0x1
	FAN_RESPONSE_INFO_NONE                      = 0x0
	FAN_UNLIMITED_MARKS                         = 0x20
	FAN_UNLIMITED_QUEUE                         = 0x10
	FD_CLOEXEC                                  = 0x1
	FD_SETSIZE                                  = 0x400
	FF0                                         = 0x0
	FIB_RULE_DEV_DETACHED                       = 0x8
	FIB_RULE_FIND_SADDR                         = 0x10000
	FIB_RULE_IIF_DETACHED                       = 0x8
	FIB_RULE_INVERT                             = 0x2
	FIB_RULE_OIF_DETACHED                       = 0x10
	FIB_RULE_PERMANENT                          = 0x1
	FIB_RULE_UNRESOLVED                         = 0x4
	FIDEDUPERANGE                               = 0xc0189436
	FSCRYPT_KEY_DESCRIPTOR_SIZE                 = 0x8
	FSCRYPT_KEY_DESC_PREFIX                     = "fscrypt:"
	FSCRYPT_KEY_DESC_PREFIX_SIZE                = 0x8
	FSCRYPT_KEY_IDENTIFIER_SIZE                 = 0x10
	FSCRYPT_KEY_REMOVAL_STATUS_FLAG_FILES_BUSY  = 0x1
	FSCRYPT_KEY_REMOVAL_STATUS_FLAG_OTHER_USERS = 0x2
	FSCRYPT_KEY_SPEC_TYPE_DESCRIPTOR            = 0x1
	FSCRYPT_KEY_SPEC_TYPE_IDENTIFIER            = 0x2
	FSCRYPT_KEY_STATUS_ABSENT                   = 0x1
	FSCRYPT_KEY_STATUS_FLAG_ADDED_BY_SELF       = 0x1
	FSCRYPT_KEY_STATUS_INCOMPLETELY_REMOVED     = 0x3
	FSCRYPT_KEY_STATUS_PRESENT                  = 0x2
	FSCRYPT_MAX_KEY_SIZE                        = 0x40
	FSCRYPT_MODE_ADIANTUM                       = 0x9
	FSCRYPT_MODE_AES_128_CBC                    = 0x5
	FSCRYPT_MODE_AES_128_CTS                    = 0x6
	FSCRYPT_MODE_AES_256_CTS                    = 0x4
	FSCRYPT_MODE_AES_256_HCTR2                  = 0xa
	FSCRYPT_MODE_AES_256_XTS                    = 0x1
	FSCRYPT_MODE_SM4_CTS                        = 0x8
	FSCRYPT_MODE_SM4_XTS                        = 0x7
	FSCRYPT_POLICY_FLAGS_PAD_16                 = 0x2
	FSCRYPT_POLICY_FLAGS_PAD_32                 = 0x3
	FSCRYPT_POLICY_FLAGS_PAD_4                  = 0x0
	FSCRYPT_POLICY_FLAGS_PAD_8                  = 0x1
	FSCRYPT_POLICY_FLAGS_PAD_MASK               = 0x3
	FSCRYPT_POLICY_FLAG_DIRECT_KEY              = 0x4
	FSCRYPT_POLICY_FLAG_IV_INO_LBLK_32          = 0x10
	FSCRYPT_POLICY_FLAG_IV_INO_LBLK_64          = 0x8
	FSCRYPT_POLICY_V1                           = 0x0
	FSCRYPT_POLICY_V2                           = 0x2
	FS_ENCRYPTION_MODE_ADIANTUM                 = 0x9
	FS_ENCRYPTION_MODE_AES_128_CBC              = 0x5
	FS_ENCRYPTION_MODE_AES_128_CTS              = 0x6
	FS_ENCRYPTION_MODE_AES_256_CBC              = 0x3
	FS_ENCRYPTION_MODE_AES_256_CTS              = 0x4
	FS_ENCRYPTION_MODE_AES_256_GCM              = 0x2
	FS_ENCRYPTION_MODE_AES_256_XTS              = 0x1
	FS_ENCRYPTION_MODE_INVALID                  = 0x0
	FS_IOC_ADD_ENCRYPTION_KEY                   = 0xc0506617
	FS_IOC_GET_ENCRYPTION_KEY_STATUS            = 0xc080661a
	FS_IOC_GET_ENCRYPTION_POLICY_EX             = 0xc0096616
	FS_IOC_MEASURE_VERITY                       = 0xc0046686
	FS_IOC_READ_VERITY_METADATA                 = 0xc0286687
	FS_IOC_REMOVE_ENCRYPTION_KEY                = 0xc0406618
	FS_IOC_REMOVE_ENCRYPTION_KEY_ALL_USERS      = 0xc0406619
	FS_KEY_DESCRIPTOR_SIZE                      = 0x8
	FS_KEY_DESC_PREFIX                          = "fscrypt:"
	FS_KEY_DESC_PREFIX_SIZE                     = 0x8
	FS_MAX_KEY_SIZE                             = 0x40
	FS_POLICY_FLAGS_PAD_16                      = 0x2
	FS_POLICY_FLAGS_PAD_32                      = 0x3
	FS_POLICY_FLAGS_PAD_4                       = 0x0
	FS_POLICY_FLAGS_PAD_8                       = 0x1
	FS_POLICY_FLAGS_PAD_MASK                    = 0x3
	FS_POLICY_FLAGS_VALID                       = 0x7
	FS_VERITY_FL                                = 0x100000
	FS_VERITY_HASH_ALG_SHA256                   = 0x1
	FS_VERITY_HASH_ALG_SHA512                   = 0x2
	FS_VERITY_METADATA_TYPE_DESCRIPTOR          = 0x2
	FS_VERITY_METADATA_TYPE_MERKLE_TREE         = 0x1
	FS_VERITY_METADATA_TYPE_SIGNATURE           = 0x3
	FUSE_SUPER_MAGIC                            = 0x65735546
	FUTEXFS_SUPER_MAGIC                         = 0xbad1dea
	F_ADD_SEALS                                 = 0x409
	F_DUPFD                                     = 0x0
	F_DUPFD_CLOEXEC                             = 0x406
	F_EXLCK                                     = 0x4
	F_GETFD                                     = 0x1
	F_GETFL                                     = 0x3
	F_GETLEASE                                  = 0x401
	F_GETOWN_EX                                 = 0x10
	F_GETPIPE_SZ                                = 0x408
	F_GETSIG                                    = 0xb
	F_GET_FILE_RW_HINT                          = 0x40d
	F_GET_RW_HINT                               = 0x40b
	F_GET_SEALS                                 = 0x40a
	F_LOCK                                      = 0x1
	F_NOTIFY                                    = 0x402
	F_OFD_GETLK                                 = 0x24
	F_OFD_SETLK                                 = 0x25
	F_OFD_SETLKW                                = 0x26
	F_OK                                        = 0x0
	F_SEAL_FUTURE_WRITE                         = 0x10
	F_SEAL_GROW                                 = 0x4
	F_SEAL_SEAL                                 = 0x1
	F_SEAL_SHRINK                               = 0x2
	F_SEAL_WRITE                                = 0x8
	F_SETFD                                     = 0x2
	F_SETFL                                     = 0x4
	F_SETLEASE                                  = 0x400
	F_SETOWN_EX                                 = 0xf
	F_SETPIPE_SZ                                = 0x407
	F_SETSIG                                    = 0xa
	F_SET_FILE_RW_HINT                          = 0x40e
	F_SET_RW_HINT                               = 0x40c
	F_SHLCK                                     = 0x8
	F_TEST                                      = 0x3
	F_TLOCK                                     = 0x2
	F_ULOCK                                     = 0x0
	GENL_ADMIN_PERM                             = 0x1
	GENL_CMD_CAP_DO                             = 0x2
	GENL_CMD_CAP_DUMP                           = 0x4
	GENL_CMD_CAP_HASPOL                         = 0x8
	GENL_HDRLEN                                 = 0x4
	GENL_ID_CTRL                                = 0x10
	GENL_ID_PMCRAID                             = 0x12
	GENL_ID_VFS_DQUOT                           = 0x11
	GENL_MAX_ID                                 = 0x3ff
	GENL_MIN_ID                                 = 0x10
	GENL_NAMSIZ                                 = 0x10
	GENL_START_ALLOC                            = 0x13
	GENL_UNS_ADMIN_PERM                         = 0x10
	GRND_INSECURE                               = 0x4
	GRND_NONBLOCK                               = 0x1
	GRND_RANDOM                                 = 0x2
	HDIO_DRIVE_CMD                              = 0x31f
	HDIO_DRIVE_CMD_AEB                          = 0x31e
	HDIO_DRIVE_CMD_HDR_SIZE                     = 0x4
	HDIO_DRIVE_HOB_HDR_SIZE                     = 0x8
	HDIO_DRIVE_RESET                            = 0x31c
	HDIO_DRIVE_TASK                             = 0x31e
	HDIO_DRIVE_TASKFILE                         = 0x31d
	HDIO_DRIVE_TASK_HDR_SIZE                    = 0x8
	HDIO_GETGEO                                 = 0x301
	HDIO_GET_32BIT                              = 0x309
	HDIO_GET_ACOUSTIC                           = 0x30f
	HDIO_GET_ADDRESS                            = 0x310
	HDIO_GET_BUSSTATE                           = 0x31a
	HDIO_GET_DMA                                = 0x30b
	HDIO_GET_IDENTITY                           = 0x30d
	HDIO_GET_KEEPSETTINGS                       = 0x308
	HDIO_GET_MULTCOUNT                          = 0x304
	HDIO_GET_NICE                               = 0x30c
	HDIO_GET_NOWERR                             = 0x30a
	HDIO_GET_QDMA                               = 0x305
	HDIO_GET_UNMASKINTR                         = 0x302
	HDIO_GET_WCACHE                             = 0x30e
	HDIO_OBSOLETE_IDENTITY                      = 0x307
	HDIO_SCAN_HWIF                              = 0x328
	HDIO_SET_32BIT                              = 0x324
	HDIO_SET_ACOUSTIC                           = 0x32c
	HDIO_SET_ADDRESS                            = 0x32f
	HDIO_SET_BUSSTATE                           = 0x32d
	HDIO_SET_DMA                                = 0x326
	HDIO_SET_KEEPSETTINGS                       = 0x323
	HDIO_SET_MULTCOUNT                          = 0x321
	HDIO_SET_NICE                               = 0x329
	HDIO_SET_NOWERR                             = 0x325
	HDIO_SET_PIO_MODE                           = 0x327
	HDIO_SET_QDMA                               = 0x32e
	HDIO_SET_UNMASKINTR                         = 0x322
	HDIO_SET_WCACHE                             = 0x32b
	HDIO_SET_XFER                               = 0x306
	HDIO_TRISTATE_HWIF                          = 0x31b
	HDIO_UNREGISTER_HWIF                        = 0x32a
	HID_MAX_DESCRIPTOR_SIZE                     = 0x1000
	HOSTFS_SUPER_MAGIC                          = 0xc0ffee
	HPFS_SUPER_MAGIC                            = 0xf995e849
	HUGETLBFS_MAGIC                             = 0x958458f6
	IBSHIFT                                     = 0x10
	ICRNL                                       = 0x100
	IFA_F_DADFAILED                             = 0x8
	IFA_F_DEPRECATED                            = 0x20
	IFA_F_HOMEADDRESS                           = 0x10
	IFA_F_MANAGETEMPADDR                        = 0x100
	IFA_F_MCAUTOJOIN                            = 0x400
	IFA_F_NODAD                                 = 0x2
	IFA_F_NOPREFIXROUTE                         = 0x200
	IFA_F_OPTIMISTIC                            = 0x4
	IFA_F_PERMANENT                             = 0x80
	IFA_F_SECONDARY                             = 0x1
	IFA_F_STABLE_PRIVACY                        = 0x800
	IFA_F_TEMPORARY                             = 0x1
	IFA_F_TENTATIVE                             = 0x40
	IFA_MAX                                     = 0xb
	IFF_ALLMULTI                                = 0x200
	IFF_ATTACH_QUEUE                            = 0x200
	IFF_AUTOMEDIA                               = 0x4000
	IFF_BROADCAST                               = 0x2
	IFF_DEBUG                                   = 0x4
	IFF_DETACH_QUEUE                            = 0x400
	IFF_DORMANT                                 = 0x20000
	IFF_DYNAMIC                                 = 0x8000
	IFF_ECHO                                    = 0x40000
	IFF_LOOPBACK                                = 0x8
	IFF_LOWER_UP                                = 0x10000
	IFF_MASTER                                  = 0x400
	IFF_MULTICAST                               = 0x1000
	IFF_MULTI_QUEUE                             = 0x100
	IFF_NAPI                                    = 0x10
	IFF_NAPI_FRAGS                              = 0x20
	IFF_NOARP                                   = 0x80
	IFF_NOFILTER                                = 0x1000
	IFF_NOTRAILERS                              = 0x20
	IFF_NO_CARRIER                              = 0x40
	IFF_NO_PI                                   = 0x1000
	IFF_ONE_QUEUE                               = 0x2000
	IFF_PERSIST                                 = 0x800
	IFF_POINTOPOINT                             = 0x10
	IFF_PORTSEL                                 = 0x2000
	IFF_PROMISC                                 = 0x100
	IFF_RUNNING                                 = 0x40
	IFF_SLAVE                                   = 0x800
	IFF_TAP                                     = 0x2
	IFF_TUN                                     = 0x1
	IFF_TUN_EXCL                                = 0x8000
	IFF_UP                                      = 0x1
	IFF_VNET_HDR                                = 0x4000
	IFF_VOLATILE                                = 0x70c5a
	IFNAMSIZ                                    = 0x10
	IGNBRK                                      = 0x1
	IGNCR                                       = 0x80
	IGNPAR                                      = 0x4
	IMAXBEL                                     = 0x2000
	INLCR                                       = 0x40
	INPCK                                       = 0x10
	IN_ACCESS                                   = 0x1
	IN_ALL_EVENTS                               = 0xfff
	IN_ATTRIB                                   = 0x4
	IN_CLASSA_HOST                              = 0xffffff
	IN_CLASSA_MAX                               = 0x80
	IN_CLASSA_NET                               = 0xff000000
	IN_CLASSA_NSHIFT                            = 0x18
	IN_CLASSB_HOST                              = 0xffff
	IN_CLASSB_MAX                               = 0x10000
	IN_CLASSB_NET                               = 0xffff0000
	IN_CLASSB_NSHIFT                            = 0x10
	IN_CLASSC_HOST                              = 0xff
	IN_CLASSC_NET                               = 0xffffff00
	IN_CLASSC_NSHIFT                            = 0x8
	IN_CLOSE                                    = 0x18
	IN_CLOSE_NOWRITE                            = 0x10
	IN_CLOSE_WRITE                              = 0x8
	IN_CREATE                                   = 0x100
	IN_DELETE                                   = 0x200
	IN_DELETE_SELF                              = 0x400
	IN_DONT_FOLLOW                              = 0x2000000
	IN_EXCL_UNLINK                              = 0x4000000
	IN_IGNORED                                  = 0x8000
	IN_ISDIR                                    = 0x40000000
	IN_LOOPBACKNET                              = 0x7f
	IN_MASK_ADD                                 = 0x20000000
	IN_MASK_CREATE                              = 0x10000000
	IN_MODIFY                                   = 0x2
	IN_MOVE                                     = 0xc0
	IN_MOVED_FROM                               = 0x40
	IN_MOVED_TO                                 = 0x80
	IN_MOVE_SELF                                = 0x800
	IN_ONESHOT                                  = 0x80000000
	IN_ONLYDIR                                  = 0x1000000
	IN_OPEN                                     = 0x20
	IN_Q_OVERFLOW                               = 0x4000
	IN_UNMOUNT                                  = 0x2000
	IPPROTO_AH                                  = 0x33
	IPPROTO_BEETPH                              = 0x5e
	IPPROTO_COMP                                = 0x6c
	IPPROTO_DCCP                                = 0x21
	IPPROTO_DSTOPTS                             = 0x3c
	IPPROTO_EGP                                 = 0x8
	IPPROTO_ENCAP                               = 0x62
	IPPROTO_ESP                                 = 0x32
	IPPROTO_ETHERNET                            = 0x8f
	IPPROTO_FRAGMENT                            = 0x2c
	IPPROTO_GRE                                 = 0x2f
	IPPROTO_HOPOPTS                             = 0x0
	IPPROTO_ICMP                                = 0x1
	IPPROTO_ICMPV6                              = 0x3a
	IPPROTO_IDP                                 = 0x16
	IPPROTO_IGMP                                = 0x2
	IPPROTO_IP                                  = 0x0
	IPPROTO_IPIP                                = 0x4
	IPPROTO_IPV6                                = 0x29
	IPPROTO_L2TP                                = 0x73
	IPPROTO_MH                                  = 0x87
	IPPROTO_MPLS                                = 0x89
	IPPROTO_MPTCP                               = 0x106
	IPPROTO_MTP                                 = 0x5c
	IPPROTO_NONE                                = 0x3b
	IPPROTO_PIM                                 = 0x67
	IPPROTO_PUP                                 = 0xc
	IPPROTO_RAW                                 = 0xff
	IPPROTO_ROUTING                             = 0x2b
	IPPROTO_RSVP                                = 0x2e
	IPPROTO_SCTP                                = 0x84
	IPPROTO_TCP                                 = 0x6
	IPPROTO_TP                                  = 0x1d
	IPPROTO_UDP                                 = 0x11
	IPPROTO_UDPLITE                             = 0x88
	IPV6_2292DSTOPTS                            = 0x4
	IPV6_2292HOPLIMIT                           = 0x8
	IPV6_2292HOPOPTS                            = 0x3
	IPV6_2292PKTINFO                            = 0x2
	IPV6_2292PKTOPTIONS                         = 0x6
	IPV6_2292RTHDR                              = 0x5
	IPV6_ADDRFORM                               = 0x1
	IPV6_ADDR_PREFERENCES                       = 0x48
	IPV6_ADD_MEMBERSHIP                         = 0x14
	IPV6_AUTHHDR                                = 0xa
	IPV6_AUTOFLOWLABEL                          = 0x46
	IPV6_CHECKSUM                               = 0x7
	IPV6_DONTFRAG                               = 0x3e
	IPV6_DROP_MEMBERSHIP                        = 0x15
	IPV6_DSTOPTS                                = 0x3b
	IPV6_FLOW                                   = 0x11
	IPV6_FREEBIND                               = 0x4e
	IPV6_HDRINCL                                = 0x24
	IPV6_HOPLIMIT                               = 0x34
	IPV6_HOPOPTS                                = 0x36
	IPV6_IPSEC_POLICY                           = 0x22
	IPV6_JOIN_ANYCAST                           = 0x1b
	IPV6_JOIN_GROUP                             = 0x14
	IPV6_LEAVE_ANYCAST                          = 0x1c
	IPV6_LEAVE_GROUP                            = 0x15
	IPV6_MINHOPCOUNT                            = 0x49
	IPV6_MTU                                    = 0x18
	IPV6_MTU_DISCOVER                           = 0x17
	IPV6_MULTICAST_ALL                          = 0x1d
	IPV6_MULTICAST_HOPS                         = 0x12
	IPV6_MULTICAST_IF                           = 0x11
	IPV6_MULTICAST_LOOP                         = 0x13
	IPV6_NEXTHOP                                = 0x9
	IPV6_ORIGDSTADDR                            = 0x4a
	IPV6_PATHMTU                                = 0x3d
	IPV6_PKTINFO                                = 0x32
	IPV6_PMTUDISC_DO                            = 0x2
	IPV6_PMTUDISC_DONT                          = 0x0
	IPV6_PMTUDISC_INTERFACE                     = 0x4
	IPV6_PMTUDISC_OMIT                          = 0x5
	IPV6_PMTUDISC_PROBE                         = 0x3
	IPV6_PMTUDISC_WANT                          = 0x1
	IPV6_RECVDSTOPTS                            = 0x3a
	IPV6_RECVERR                                = 0x19
	IPV6_RECVERR_RFC4884                        = 0x1f
	IPV6_RECVFRAGSIZE                           = 0x4d
	IPV6_RECVHOPLIMIT                           = 0x33
	IPV6_RECVHOPOPTS                            = 0x35
	IPV6_RECVORIGDSTADDR                        = 0x4a
	IPV6_RECVPATHMTU                            = 0x3c
	IPV6_RECVPKTINFO                            = 0x31
	IPV6_RECVRTHDR                              = 0x38
	IPV6_RECVTCLASS                             = 0x42
	IPV6_ROUTER_ALERT                           = 0x16
	IPV6_ROUTER_ALERT_ISOLATE                   = 0x1e
	IPV6_RTHDR                                  = 0x39
	IPV6_RTHDRDSTOPTS                           = 0x37
	IPV6_RTHDR_LOOSE                            = 0x0
	IPV6_RTHDR_STRICT                           = 0x1
	IPV6_RTHDR_TYPE_0                           = 0x0
	IPV6_RXDSTOPTS                              = 0x3b
	IPV6_RXHOPOPTS                              = 0x36
	IPV6_TCLASS                                 = 0x43
	IPV6_TRANSPARENT                            = 0x4b
	IPV6_UNICAST_HOPS                           = 0x10
	IPV6_UNICAST_IF                             = 0x4c
	IPV6_USER_FLOW                              = 0xe
	IPV6_V6ONLY                                 = 0x1a
	IPV6_XFRM_POLICY                            = 0x23
	IP_ADD_MEMBERSHIP                           = 0x23
	IP_ADD_SOURCE_MEMBERSHIP                    = 0x27
	IP_BIND_ADDRESS_NO_PORT                     = 0x18
	IP_BLOCK_SOURCE                             = 0x26
	IP_CHECKSUM                                 = 0x17
	IP_DEFAULT_MULTICAST_LOOP                   = 0x1
	IP_DEFAULT_MULTICAST_TTL                    = 0x1
	IP_DF                                       = 0x4000
	IP_DROP_MEMBERSHIP                          = 0x24
	IP_DROP_SOURCE_MEMBERSHIP                   = 0x28
	IP_FREEBIND                                 = 0xf
	IP_HDRINCL                                  = 0x3
	IP_IPSEC_POLICY                             = 0x10
	IP_MAXPACKET                                = 0xffff
	IP_MAX_MEMBERSHIPS                          = 0x14
	IP_MF                                       = 0x2000
	IP_MINTTL                                   = 0x15
	IP_MSFILTER                                 = 0x29
	IP_MSS                                      = 0x240
	IP_MTU                                      = 0xe
	IP_MTU_DISCOVER                             = 0xa
	IP_MULTICAST_ALL                            = 0x31
	IP_MULTICAST_IF                             = 0x20
	IP_MULTICAST_LOOP                           = 0x22
	IP_MULTICAST_TTL                            = 0x21
	IP_NODEFRAG                                 = 0x16
	IP_OFFMASK                                  = 0x1fff
	IP_OPTIONS                                  = 0x4
	IP_ORIGDSTADDR                              = 0x14
	IP_PASSSEC                                  = 0x12
	IP_PKTINFO                                  = 0x8
	IP_PKTOPTIONS                               = 0x9
	IP_PMTUDISC                                 = 0xa
	IP_PMTUDISC_DO                              = 0x2
	IP_PMTUDISC_DONT                            = 0x0
	IP_PMTUDISC_INTERFACE                       = 0x4
	IP_PMTUDISC_OMIT                            = 0x5
	IP_PMTUDISC_PROBE                           = 0x3
	IP_PMTUDISC_WANT                            = 0x1
	IP_RECVERR                                  = 0xb
	IP_RECVERR_RFC4884                          = 0x1a
	IP_RECVFRAGSIZE                             = 0x19
	IP_RECVOPTS                                 = 0x6
	IP_RECVORIGDSTADDR                          = 0x14
	IP_RECVRETOPTS                              = 0x7
	IP_RECVTOS                                  = 0xd
	IP_RECVTTL                                  = 0xc
	IP_RETOPTS                                  = 0x7
	IP_RF                                       = 0x8000
	IP_ROUTER_ALERT                             = 0x5
	IP_TOS                                      = 0x1
	IP_TRANSPARENT                              = 0x13
	IP_TTL                                      = 0x2
	IP_UNBLOCK_SOURCE                           = 0x25
	IP_UNICAST_IF                               = 0x32
	IP_USER_FLOW                                = 0xd
	IP_XFRM_POLICY                              = 0x11
	ISOFS_SUPER_MAGIC                           = 0x9660
	ISTRIP                                      = 0x20
	ITIMER_PROF                                 = 0x2
	ITIMER_REAL                                 = 0x0
	ITIMER_VIRTUAL                              = 0x1
	IUTF8                                       = 0x4000
	IXANY                                       = 0x800
	JFFS2_SUPER_MAGIC                           = 0x72b6
	KCMPROTO_CONNECTED                          = 0x0
	KCM_RECV_DISABLE                            = 0x1
	KEXEC_ARCH_386                              = 0x30000
	KEXEC_ARCH_68K                              = 0x40000
	KEXEC_ARCH_AARCH64                          = 0xb70000
	KEXEC_ARCH_ARM                              = 0x280000
	KEXEC_ARCH_DEFAULT                          = 0x0
	KEXEC_ARCH_IA_64                            = 0x320000
	KEXEC_ARCH_LOONGARCH                        = 0x1020000
	KEXEC_ARCH_MASK                             = 0xffff0000
	KEXEC_ARCH_MIPS                             = 0x80000
	KEXEC_ARCH_MIPS_LE                          = 0xa0000
	KEXEC_ARCH_PARISC                           = 0xf0000
	KEXEC_ARCH_PPC                              = 0x140000
	KEXEC_ARCH_PPC64                            = 0x150000
	KEXEC_ARCH_RISCV                            = 0xf30000
	KEXEC_ARCH_S390                             = 0x160000
	KEXEC_ARCH_SH                               = 0x2a0000
	KEXEC_ARCH_X86_64                           = 0x3e0000
	KEXEC_FILE_DEBUG                            = 0x8
	KEXEC_FILE_NO_INITRAMFS                     = 0x4
	KEXEC_FILE_ON_CRASH                         = 0x2
	KEXEC_FILE_UNLOAD                           = 0x1
	KEXEC_ON_CRASH                              = 0x1
	KEXEC_PRESERVE_CONTEXT                      = 0x2
	KEXEC_SEGMENT_MAX                           = 0x10
	KEXEC_UPDATE_ELFCOREHDR                     = 0x4
	KEYCTL_ASSUME_AUTHORITY                     = 0x10
	KEYCTL_CAPABILITIES                         = 0x1f
	KEYCTL_CAPS0_BIG_KEY                        = 0x10
	KEYCTL_CAPS0_CAPABILITIES                   = 0x1
	KEYCTL_CAPS0_DIFFIE_HELLMAN                 = 0x4
	KEYCTL_CAPS0_INVALIDATE                     = 0x20
	KEYCTL_CAPS0_MOVE                           = 0x80
	KEYCTL_CAPS0_PERSISTENT_KEYRINGS            = 0x2
	KEYCTL_CAPS0_PUBLIC_KEY                     = 0x8
	KEYCTL_CAPS0_RESTRICT_KEYRING               = 0x40
	KEYCTL_CAPS1_NOTIFICATIONS                  = 0x4
	KEYCTL_CAPS1_NS_KEYRING_NAME                = 0x1
	KEYCTL_CAPS1_NS_KEY_TAG                     = 0x2
	KEYCTL_CHOWN                                = 0x4
	KEYCTL_CLEAR                                = 0x7
	KEYCTL_DESCRIBE                             = 0x6
	KEYCTL_DH_COMPUTE                           = 0x17
	KEYCTL_GET_KEYRING_ID                       = 0x0
	KEYCTL_GET_PERSISTENT                       = 0x16
	KEYCTL_GET_SECURITY                         = 0x11
	KEYCTL_INSTANTIATE                          = 0xc
	KEYCTL_INSTANTIATE_IOV                      = 0x14
	KEYCTL_INVALIDATE                           = 0x15
	KEYCTL_JOIN_SESSION_KEYRING                 = 0x1
	KEYCTL_LINK                                 = 0x8
	KEYCTL_MOVE                                 = 0x1e
	KEYCTL_MOVE_EXCL                            = 0x1
	KEYCTL_NEGATE                               = 0xd
	KEYCTL_PKEY_DECRYPT                         = 0x1a
	KEYCTL_PKEY_ENCRYPT                         = 0x19
	KEYCTL_PKEY_QUERY                           = 0x18
	KEYCTL_PKEY_SIGN                            = 0x1b
	KEYCTL_PKEY_VERIFY                          = 0x1c
	KEYCTL_READ                                 = 0xb
	KEYCTL_REJECT                               = 0x13
	KEYCTL_RESTRICT_KEYRING                     = 0x1d
	KEYCTL_REVOKE                               = 0x3
	KEYCTL_SEARCH                               = 0xa
	KEYCTL_SESSION_TO_PARENT                    = 0x12
	KEYCTL_SETPERM                              = 0x5
	KEYCTL_SET_REQKEY_KEYRING                   = 0xe
	KEYCTL_SET_TIMEOUT                          = 0xf
	KEYCTL_SUPPORTS_DECRYPT                     = 0x2
	KEYCTL_SUPPORTS_ENCRYPT                     = 0x1
	KEYCTL_SUPPORTS_SIGN                        = 0x4
	KEYCTL_SUPPORTS_VERIFY                      = 0x8
	KEYCTL_UNLINK                               = 0x9
	KEYCTL_UPDATE                               = 0x2
	KEYCTL_WATCH_KEY                            = 0x20
	KEY_REQKEY_DEFL_DEFAULT                     = 0x0
	KEY_REQKEY_DEFL_GROUP_KEYRING               = 0x6
	KEY_REQKEY_DEFL_NO_CHANGE                   = -0x1
	KEY_REQKEY_DEFL_PROCESS_KEYRING             = 0x2
	KEY_REQKEY_DEFL_REQUESTOR_KEYRING           = 0x7
	KEY_REQKEY_DEFL_SESSION_KEYRING             = 0x3
	KEY_REQKEY_DEFL_THREAD_KEYRING              = 0x1
	KEY_REQKEY_DEFL_USER_KEYRING                = 0x4
	KEY_REQKEY_DEFL_USER_SESSION_KEYRING        = 0x5
	KEY_SPEC_GROUP_KEYRING                      = -0x6
	KEY_SPEC_PROCESS_KEYRING                    = -0x2
	KEY_SPEC_REQKEY_AUTH_KEY                    = -0x7
	KEY_SPEC_REQUESTOR_KEYRING                  = -0x8
	KEY_SPEC_SESSION_KEYRING                    = -0x3
	KEY_SPEC_THREAD_KEYRING                     = -0x1
	KEY_SPEC_USER_KEYRING                       = -0x4
	KEY_SPEC_USER_SESSION_KEYRING               = -0x5
	LANDLOCK_ACCESS_FS_EXECUTE                  = 0x1
	LANDLOCK_ACCESS_FS_MAKE_BLOCK               = 0x800
	LANDLOCK_ACCESS_FS_MAKE_CHAR                = 0x40
	LANDLOCK_ACCESS_FS_MAKE_DIR                 = 0x80
	LANDLOCK_ACCESS_FS_MAKE_FIFO                = 0x400
	LANDLOCK_ACCESS_FS_MAKE_REG                 = 0x100
	LANDLOCK_ACCESS_FS_MAKE_SOCK                = 0x200
	LANDLOCK_ACCESS_FS_MAKE_SYM                 = 0x1000
	LANDLOCK_ACCESS_FS_READ_DIR                 = 0x8
	LANDLOCK_ACCESS_FS_READ_FILE                = 0x4
	LANDLOCK_ACCESS_FS_REFER                    = 0x2000
	LANDLOCK_ACCESS_FS_REMOVE_DIR               = 0x10
	LANDLOCK_ACCESS_FS_REMOVE_FILE              = 0x20
	LANDLOCK_ACCESS_FS_TRUNCATE                 = 0x4000
	LANDLOCK_ACCESS_FS_WRITE_FILE               = 0x2
	LANDLOCK_ACCESS_NET_BIND_TCP                = 0x1
	LANDLOCK_ACCESS_NET_CONNECT_TCP             = 0x2
	LANDLOCK_CREATE_RULESET_VERSION             = 0x1
	LINUX_REBOOT_CMD_CAD_OFF                    = 0x0
	LINUX_REBOOT_CMD_CAD_ON                     = 0x89abcdef
	LINUX_REBOOT_CMD_HALT                       = 0xcdef0123
	LINUX_REBOOT_CMD_KEXEC                      = 0x45584543
	LINUX_REBOOT_CMD_POWER_OFF                  = 0x4321fedc
	LINUX_REBOOT_CMD_RESTART                    = 0x1234567
	LINUX_REBOOT_CMD_RESTART2                   = 0xa1b2c3d4
	LINUX_REBOOT_CMD_SW_SUSPEND                 = 0xd000fce2
	LINUX_REBOOT_MAGIC1                         = 0xfee1dead
	LINUX_REBOOT_MAGIC2                         = 0x28121969
	LOCK_EX                                     = 0x2
	LOCK_NB                                     = 0x4
	LOCK_SH                                     = 0x1
	LOCK_UN                                     = 0x8
	LOOP_CLR_FD                                 = 0x4c01
	LOOP_CONFIGURE                              = 0x4c0a
	LOOP_CTL_ADD                                = 0x4c80
	LOOP_CTL_GET_FREE                           = 0x4c82
	LOOP_CTL_REMOVE                             = 0x4c81
	LOOP_GET_STATUS                             = 0x4c03
	LOOP_GET_STATUS64                           = 0x4c05
	LOOP_SET_BLOCK_SIZE                         = 0x4c09
	LOOP_SET_CAPACITY                           = 0x4c07
	LOOP_SET_DIRECT_IO                          = 0x4c08
	LOOP_SET_FD                                 = 0x4c00
	LOOP_SET_STATUS                             = 0x4c02
	LOOP_SET_STATUS64                           = 0x4c04
	LOOP_SET_STATUS_CLEARABLE_FLAGS             = 0x4
	LOOP_SET_STATUS_SETTABLE_FLAGS              = 0xc
	LO_KEY_SIZE                                 = 0x20
	LO_NAME_SIZE                                = 0x40
	LWTUNNEL_IP6_MAX                            = 0x8
	LWTUNNEL_IP_MAX                             = 0x8
	LWTUNNEL_IP_OPTS_MAX                        = 0x3
	LWTUNNEL_IP_OPT_ERSPAN_MAX                  = 0x4
	LWTUNNEL_IP_OPT_GENEVE_MAX                  = 0x3
	LWTUNNEL_IP_OPT_VXLAN_MAX                   = 0x1
	MADV_COLD                                   = 0x14
	MADV_COLLAPSE                               = 0x19
	MADV_DODUMP                                 = 0x11
	MADV_DOFORK                                 = 0xb
	MADV_DONTDUMP                               = 0x10
	MADV_DONTFORK                               = 0xa
	MADV_DONTNEED                               = 0x4
	MADV_DONTNEED_LOCKED                        = 0x18
	MADV_FREE                                   = 0x8
	MADV_HUGEPAGE                               = 0xe
	MADV_HWPOISON                               = 0x64
	MADV_KEEPONFORK                             = 0x13
	MADV_MERGEABLE                              = 0xc
	MADV_NOHUGEPAGE                             = 0xf
	MADV_NORMAL                                 = 0x0
	MADV_PAGEOUT                                = 0x15
	MADV_POPULATE_READ                          = 0x16
	MADV_POPULATE_WRITE                         = 0x17
	MADV_RANDOM                                 = 0x1
	MADV_REMOVE                                 = 0x9
	MADV_SEQUENTIAL                             = 0x2
	MADV_UNMERGEABLE                            = 0xd
	MADV_WILLNEED                               = 0x3
	MADV_WIPEONFORK                             = 0x12
	MAP_FILE                                    = 0x0
	MAP_FIXED                                   = 0x10
	MAP_FIXED_NOREPLACE                         = 0x100000
	MAP_HUGE_MASK                               = 0x3f
	MAP_HUGE_SHIFT                              = 0x1a
	MAP_PRIVATE                                 = 0x2
	MAP_SHARED                                  = 0x1
	MAP_SHARED_VALIDATE                         = 0x3
	MAP_TYPE                                    = 0xf
	MCAST_BLOCK_SOURCE                          = 0x2b
	MCAST_EXCLUDE                               = 0x0
	MCAST_INCLUDE                               = 0x1
	MCAST_JOIN_GROUP                            = 0x2a
	MCAST_JOIN_SOURCE_GROUP                     = 0x2e
	MCAST_LEAVE_GROUP                           = 0x2d
	MCAST_LEAVE_SOURCE_GROUP                    = 0x2f
	MCAST_MSFILTER                              = 0x30
	MCAST_UNBLOCK_SOURCE                        = 0x2c
	MEMGETREGIONINFO                            = 0xc0104d08
	MEMREADOOB64                                = 0xc0184d16
	MEMWRITE                                    = 0xc0304d18
	MEMWRITEOOB64                               = 0xc0184d15
	MFD_ALLOW_SEALING                           = 0x2
	MFD_CLOEXEC                                 = 0x1
	MFD_EXEC                                    = 0x10
	MFD_HUGETLB                                 = 0x4
	MFD_HUGE_16GB                               = 0x88000000
	MFD_HUGE_16MB                               = 0x60000000
	MFD_HUGE_1GB                                = 0x78000000
	MFD_HUGE_1MB                                = 0x50000000
	MFD_HUGE_256MB                              = 0x70000000
	MFD_HUGE_2GB                                = 0x7c000000
	MFD_HUGE_2MB                                = 0x54000000
	MFD_HUGE_32MB                               = 0x64000000
	MFD_HUGE_512KB                              = 0x4c000000
	MFD_HUGE_512MB                              = 0x74000000
	MFD_HUGE_64KB                               = 0x40000000
	MFD_HUGE_8MB                                = 0x5c000000
	MFD_HUGE_MASK                               = 0x3f
	MFD_HUGE_SHIFT                              = 0x1a
	MFD_NOEXEC_SEAL                             = 0x8
	MINIX2_SUPER_MAGIC                          = 0x2468
	MINIX2_SUPER_MAGIC2                         = 0x2478
	MINIX3_SUPER_MAGIC                          = 0x4d5a
	MINIX_SUPER_MAGIC                           = 0x137f
	MINIX_SUPER_MAGIC2                          = 0x138f
	MNT_DETACH                                  = 0x2
	MNT_EXPIRE                                  = 0x4
	MNT_FORCE                                   = 0x1
	MNT_ID_REQ_SIZE_VER0                        = 0x18
	MODULE_INIT_COMPRESSED_FILE                 = 0x4
	MODULE_INIT_IGNORE_MODVERSIONS              = 0x1
	MODULE_INIT_IGNORE_VERMAGIC                 = 0x2
	MOUNT_ATTR_IDMAP                            = 0x100000
	MOUNT_ATTR_NOATIME                          = 0x10
	MOUNT_ATTR_NODEV                            = 0x4
	MOUNT_ATTR_NODIRATIME                       = 0x80
	MOUNT_ATTR_NOEXEC                           = 0x8
	MOUNT_ATTR_NOSUID                           = 0x2
	MOUNT_ATTR_NOSYMFOLLOW                      = 0x200000
	MOUNT_ATTR_RDONLY                           = 0x1
	MOUNT_ATTR_RELATIME                         = 0x0
	MOUNT_ATTR_SIZE_VER0                        = 0x20
	MOUNT_ATTR_STRICTATIME                      = 0x20
	MOUNT_ATTR__ATIME                           = 0x70
	MREMAP_DONTUNMAP                            = 0x4
	MREMAP_FIXED                                = 0x2
	MREMAP_MAYMOVE                              = 0x1
	MSDOS_SUPER_MAGIC                           = 0x4d44
	MSG_BATCH                                   = 0x40000
	MSG_CMSG_CLOEXEC                            = 0x40000000
	MSG_CONFIRM                                 = 0x800
	MSG_CTRUNC                                  = 0x8
	MSG_DONTROUTE                               = 0x4
	MSG_DONTWAIT                                = 0x40
	MSG_EOR                                     = 0x80
	MSG_ERRQUEUE                                = 0x2000
	MSG_FASTOPEN                                = 0x20000000
	MSG_FIN                                     = 0x200
	MSG_MORE                                    = 0x8000
	MSG_NOSIGNAL                                = 0x4000
	MSG_OOB                                     = 0x1
	MSG_PEEK                                    = 0x2
	MSG_PROXY                                   = 0x10
	MSG_RST                                     = 0x1000
	MSG_SYN                                     = 0x400
	MSG_TRUNC                                   = 0x20
	MSG_TRYHARD                                 = 0x4
	MSG_WAITALL                                 = 0x100
	MSG_WAITFORONE                              = 0x10000
	MSG_ZEROCOPY                                = 0x4000000
	MS_ACTIVE                                   = 0x40000000
	MS_ASYNC                                    = 0x1
	MS_BIND                                     = 0x1000
	MS_BORN                                     = 0x20000000
	MS_DIRSYNC                                  = 0x80
	MS_INVALIDATE                               = 0x2
	MS_I_VERSION                                = 0x800000
	MS_KERNMOUNT                                = 0x400000
	MS_LAZYTIME                                 = 0x2000000
	MS_MANDLOCK                                 = 0x40
	MS_MGC_MSK                                  = 0xffff0000
	MS_MGC_VAL                                  = 0xc0ed0000
	MS_MOVE                                     = 0x2000
	MS_NOATIME                                  = 0x400
	MS_NODEV                                    = 0x4
	MS_NODIRATIME                               = 0x800
	MS_NOEXEC                                   = 0x8
	MS_NOREMOTELOCK                             = 0x8000000
	MS_NOSEC                                    = 0x10000000
	MS_NOSUID                                   = 0x2
	MS_NOSYMFOLLOW                              = 0x100
	MS_NOUSER                                   = -0x80000000
	MS_POSIXACL                                 = 0x10000
	MS_PRIVATE                                  = 0x40000
	MS_RDONLY                                   = 0x1
	MS_REC                                      = 0x4000
	MS_RELATIME                                 = 0x200000
	MS_REMOUNT                                  = 0x20
	MS_RMT_MASK                                 = 0x2800051
	MS_SHARED                                   = 0x100000
	MS_SILENT                                   = 0x8000
	MS_SLAVE                                    = 0x80000
	MS_STRICTATIME                              = 0x1000000
	MS_SUBMOUNT                                 = 0x4000000
	MS_SYNC                                     = 0x4
	MS_SYNCHRONOUS                              = 0x10
	MS_UNBINDABLE                               = 0x20000
	MS_VERBOSE                                  = 0x8000
	MTD_ABSENT                                  = 0x0
	MTD_BIT_WRITEABLE                           = 0x800
	MTD_CAP_NANDFLASH                           = 0x400
	MTD_CAP_NORFLASH                            = 0xc00
	MTD_CAP_NVRAM                               = 0x1c00
	MTD_CAP_RAM                                 = 0x1c00
	MTD_CAP_ROM                                 = 0x0
	MTD_DATAFLASH                               = 0x6
	MTD_INODE_FS_MAGIC                          = 0x11307854
	MTD_MAX_ECCPOS_ENTRIES                      = 0x40
	MTD_MAX_OOBFREE_ENTRIES                     = 0x8
	MTD_MLCNANDFLASH                            = 0x8
	MTD_NANDECC_AUTOPLACE                       = 0x2
	MTD_NANDECC_AUTOPL_USR                      = 0x4
	MTD_NANDECC_OFF                             = 0x0
	MTD_NANDECC_PLACE                           = 0x1
	MTD_NANDECC_PLACEONLY                       = 0x3
	MTD_NANDFLASH                               = 0x4
	MTD_NORFLASH                                = 0x3
	MTD_NO_ERASE                                = 0x1000
	MTD_OTP_FACTORY                             = 0x1
	MTD_OTP_OFF                                 = 0x0
	MTD_OTP_USER                                = 0x2
	MTD_POWERUP_LOCK                            = 0x2000
	MTD_RAM                                     = 0x1
	MTD_ROM                                     = 0x2
	MTD_SLC_ON_MLC_EMULATION                    = 0x4000
	MTD_UBIVOLUME                               = 0x7
	MTD_WRITEABLE                               = 0x400
	NAME_MAX                                    = 0xff
	NCP_SUPER_MAGIC                             = 0x564c
	NETLINK_ADD_MEMBERSHIP                      = 0x1
	NETLINK_AUDIT                               = 0x9
	NETLINK_BROADCAST_ERROR                     = 0x4
	NETLINK_CAP_ACK                             = 0xa
	NETLINK_CONNECTOR                           = 0xb
	NETLINK_CRYPTO                              = 0x15
	NETLINK_DNRTMSG                             = 0xe
	NETLINK_DROP_MEMBERSHIP                     = 0x2
	NETLINK_ECRYPTFS                            = 0x13
	NETLINK_EXT_ACK                             = 0xb
	NETLINK_FIB_LOOKUP                          = 0xa
	NETLINK_FIREWALL                            = 0x3
	NETLINK_GENERIC                             = 0x10
	NETLINK_GET_STRICT_CHK                      = 0xc
	NETLINK_INET_DIAG                           = 0x4
	NETLINK_IP6_FW                              = 0xd
	NETLINK_ISCSI                               = 0x8
	NETLINK_KOBJECT_UEVENT                      = 0xf
	NETLINK_LISTEN_ALL_NSID                     = 0x8
	NETLINK_LIST_MEMBERSHIPS                    = 0x9
	NETLINK_NETFILTER                           = 0xc
	NETLINK_NFLOG                               = 0x5
	NETLINK_NO_ENOBUFS                          = 0x5
	NETLINK_PKTINFO                             = 0x3
	NETLINK_RDMA                                = 0x14
	NETLINK_ROUTE                               = 0x0
	NETLINK_RX_RING                             = 0x6
	NETLINK_SCSITRANSPORT                       = 0x12
	NETLINK_SELINUX                             = 0x7
	NETLINK_SMC                                 = 0x16
	NETLINK_SOCK_DIAG                           = 0x4
	NETLINK_TX_RING                             = 0x7
	NETLINK_UNUSED                              = 0x1
	NETLINK_USERSOCK                            = 0x2
	NETLINK_XFRM                                = 0x6
	NETNSA_MAX                                  = 0x5
	NETNSA_NSID_NOT_ASSIGNED                    = -0x1
	NFC_ATR_REQ_GB_MAXSIZE                      = 0x30
	NFC_ATR_REQ_MAXSIZE                         = 0x40
	NFC_ATR_RES_GB_MAXSIZE                      = 0x2f
	NFC_ATR_RES_MAXSIZE                         = 0x40
	NFC_COMM_ACTIVE                             = 0x0
	NFC_COMM_PASSIVE                            = 0x1
	NFC_DEVICE_NAME_MAXSIZE                     = 0x8
	NFC_DIRECTION_RX                            = 0x0
	NFC_DIRECTION_TX                            = 0x1
	NFC_FIRMWARE_NAME_MAXSIZE                   = 0x20
	NFC_GB_MAXSIZE                              = 0x30
	NFC_GENL_MCAST_EVENT_NAME                   = "events"
	NFC_GENL_NAME                               = "nfc"
	NFC_GENL_VERSION                            = 0x1
	NFC_HEADER_SIZE                             = 0x1
	NFC_ISO15693_UID_MAXSIZE                    = 0x8
	NFC_LLCP_MAX_SERVICE_NAME                   = 0x3f
	NFC_LLCP_MIUX                               = 0x1
	NFC_LLCP_REMOTE_LTO                         = 0x3
	NFC_LLCP_REMOTE_MIU                         = 0x2
	NFC_LLCP_REMOTE_RW                          = 0x4
	NFC_LLCP_RW                                 = 0x0
	NFC_NFCID1_MAXSIZE                          = 0xa
	NFC_NFCID2_MAXSIZE                          = 0x8
	NFC_NFCID3_MAXSIZE                          = 0xa
	NFC_PROTO_FELICA                            = 0x3
	NFC_PROTO_FELICA_MASK                       = 0x8
	NFC_PROTO_ISO14443                          = 0x4
	NFC_PROTO_ISO14443_B                        = 0x6
	NFC_PROTO_ISO14443_B_MASK                   = 0x40
	NFC_PROTO_ISO14443_MASK                     = 0x10
	NFC_PROTO_ISO15693                          = 0x7
	NFC_PROTO_ISO15693_MASK                     = 0x80
	NFC_PROTO_JEWEL                             = 0x1
	NFC_PROTO_JEWEL_MASK                        = 0x2
	NFC_PROTO_MAX                               = 0x8
	NFC_PROTO_MIFARE                            = 0x2
	NFC_PROTO_MIFARE_MASK                       = 0x4
	NFC_PROTO_NFC_DEP                           = 0x5
	NFC_PROTO_NFC_DEP_MASK                      = 0x20
	NFC_RAW_HEADER_SIZE                         = 0x2
	NFC_RF_INITIATOR                            = 0x0
	NFC_RF_NONE                                 = 0x2
	NFC_RF_TARGET                               = 0x1
	NFC_SENSB_RES_MAXSIZE                       = 0xc
	NFC_SENSF_RES_MAXSIZE                       = 0x12
	NFC_SE_DISABLED                             = 0x0
	NFC_SE_EMBEDDED                             = 0x2
	NFC_SE_ENABLED                              = 0x1
	NFC_SE_UICC                                 = 0x1
	NFC_SOCKPROTO_LLCP                          = 0x1
	NFC_SOCKPROTO_MAX                           = 0x2
	NFC_SOCKPROTO_RAW                           = 0x0
	NFNETLINK_V0                                = 0x0
	NFNLGRP_ACCT_QUOTA                          = 0x8
	NFNLGRP_CONNTRACK_DESTROY                   = 0x3
	NFNLGRP_CONNTRACK_EXP_DESTROY               = 0x6
	NFNLGRP_CONNTRACK_EXP_NEW                   = 0x4
	NFNLGRP_CONNTRACK_EXP_UPDATE                = 0x5
	NFNLGRP_CONNTRACK_NEW                       = 0x1
	NFNLGRP_CONNTRACK_UPDATE                    = 0x2
	NFNLGRP_MAX                                 = 0x9
	NFNLGRP_NFTABLES                            = 0x7
	NFNLGRP_NFTRACE                             = 0x9
	NFNLGRP_NONE                                = 0x0
	NFNL_BATCH_MAX                              = 0x1
	NFNL_MSG_BATCH_BEGIN                        = 0x10
	NFNL_MSG_BATCH_END                          = 0x11
	NFNL_NFA_NEST                               = 0x8000
	NFNL_SUBSYS_ACCT                            = 0x7
	NFNL_SUBSYS_COUNT                           = 0xd
	NFNL_SUBSYS_CTHELPER                        = 0x9
	NFNL_SUBSYS_CTNETLINK                       = 0x1
	NFNL_SUBSYS_CTNETLINK_EXP                   = 0x2
	NFNL_SUBSYS_CTNETLINK_TIMEOUT               = 0x8
	NFNL_SUBSYS_HOOK                            = 0xc
	NFNL_SUBSYS_IPSET                           = 0x6
	NFNL_SUBSYS_NFTABLES                        = 0xa
	NFNL_SUBSYS_NFT_COMPAT                      = 0xb
	NFNL_SUBSYS_NONE                            = 0x0
	NFNL_SUBSYS_OSF                             = 0x5
	NFNL_SUBSYS_QUEUE                           = 0x3
	NFNL_SUBSYS_ULOG                            = 0x4
	NFS_SUPER_MAGIC                             = 0x6969
	NFT_CHAIN_FLAGS                             = 0x7
	NFT_CHAIN_MAXNAMELEN                        = 0x100
	NFT_CT_MAX                                  = 0x17
	NFT_DATA_RESERVED_MASK                      = 0xffffff00
	NFT_DATA_VALUE_MAXLEN                       = 0x40
	NFT_EXTHDR_OP_MAX                           = 0x4
	NFT_FIB_RESULT_MAX                          = 0x3
	NFT_INNER_MASK                              = 0xf
	NFT_LOGLEVEL_MAX                            = 0x8
	NFT_NAME_MAXLEN                             = 0x100
	NFT_NG_MAX                                  = 0x1
	NFT_OBJECT_CONNLIMIT                        = 0x5
	NFT_OBJECT_COUNTER                          = 0x1
	NFT_OBJECT_CT_EXPECT                        = 0x9
	NFT_OBJECT_CT_HELPER                        = 0x3
	NFT_OBJECT_CT_TIMEOUT                       = 0x7
	NFT_OBJECT_LIMIT                            = 0x4
	NFT_OBJECT_MAX                              = 0xa
	NFT_OBJECT_QUOTA                            = 0x2
	NFT_OBJECT_SECMARK                          = 0x8
	NFT_OBJECT_SYNPROXY                         = 0xa
	NFT_OBJECT_TUNNEL                           = 0x6
	NFT_OBJECT_UNSPEC                           = 0x0
	NFT_OBJ_MAXNAMELEN                          = 0x100
	NFT_OSF_MAXGENRELEN                         = 0x10
	NFT_QUEUE_FLAG_BYPASS                       = 0x1
	NFT_QUEUE_FLAG_CPU_FANOUT                   = 0x2
	NFT_QUEUE_FLAG_MASK                         = 0x3
	NFT_REG32_COUNT                             = 0x10
	NFT_REG32_SIZE                              = 0x4
	NFT_REG_MAX                                 = 0x4
	NFT_REG_SIZE                                = 0x10
	NFT_REJECT_ICMPX_MAX                        = 0x3
	NFT_RT_MAX                                  = 0x4
	NFT_SECMARK_CTX_MAXLEN                      = 0x100
	NFT_SET_MAXNAMELEN                          = 0x100
	NFT_SOCKET_MAX                              = 0x3
	NFT_TABLE_F_MASK                            = 0x3
	NFT_TABLE_MAXNAMELEN                        = 0x100
	NFT_TRACETYPE_MAX                           = 0x3
	NFT_TUNNEL_F_MASK                           = 0x7
	NFT_TUNNEL_MAX                              = 0x1
	NFT_TUNNEL_MODE_MAX                         = 0x2
	NFT_USERDATA_MAXLEN                         = 0x100
	NFT_XFRM_KEY_MAX                            = 0x6
	NF_NAT_RANGE_MAP_IPS                        = 0x1
	NF_NAT_RANGE_MASK                           = 0x7f
	NF_NAT_RANGE_NETMAP                         = 0x40
	NF_NAT_RANGE_PERSISTENT                     = 0x8
	NF_NAT_RANGE_PROTO_OFFSET                   = 0x20
	NF_NAT_RANGE_PROTO_RANDOM                   = 0x4
	NF_NAT_RANGE_PROTO_RANDOM_ALL               = 0x14
	NF_NAT_RANGE_PROTO_RANDOM_FULLY             = 0x10
	NF_NAT_RANGE_PROTO_SPECIFIED                = 0x2
	NILFS_SUPER_MAGIC                           = 0x3434
	NL0                                         = 0x0
	NL1                                         = 0x100
	NLA_ALIGNTO                                 = 0x4
	NLA_F_NESTED                                = 0x8000
	NLA_F_NET_BYTEORDER                         = 0x4000
	NLA_HDRLEN                                  = 0x4
	NLMSG_ALIGNTO                               = 0x4
	NLMSG_DONE                                  = 0x3
	NLMSG_ERROR                                 = 0x2
	NLMSG_HDRLEN                                = 0x10
	NLMSG_MIN_TYPE                              = 0x10
	NLMSG_NOOP                                  = 0x1
	NLMSG_OVERRUN                               = 0x4
	NLM_F_ACK                                   = 0x4
	NLM_F_ACK_TLVS                              = 0x200
	NLM_F_APPEND                                = 0x800
	NLM_F_ATOMIC                                = 0x400
	NLM_F_BULK                                  = 0x200
	NLM_F_CAPPED                                = 0x100
	NLM_F_CREATE                                = 0x400
	NLM_F_DUMP                                  = 0x300
	NLM_F_DUMP_FILTERED                         = 0x20
	NLM_F_DUMP_INTR                             = 0x10
	NLM_F_ECHO                                  = 0x8
	NLM_F_EXCL                                  = 0x200
	NLM_F_MATCH                                 = 0x200
	NLM_F_MULTI                                 = 0x2
	NLM_F_NONREC                                = 0x100
	NLM_F_REPLACE                               = 0x100
	NLM_F_REQUEST                               = 0x1
	NLM_F_ROOT                                  = 0x100
	NSFS_MAGIC                                  = 0x6e736673
	OCFS2_SUPER_MAGIC                           = 0x7461636f
	OCRNL                                       = 0x8
	OFDEL                                       = 0x80
	OFILL                                       = 0x40
	ONLRET                                      = 0x20
	ONOCR                                       = 0x10
	OPENPROM_SUPER_MAGIC                        = 0x9fa1
	OPOST                                       = 0x1
	OVERLAYFS_SUPER_MAGIC                       = 0x794c7630
	O_ACCMODE                                   = 0x3
	O_RDONLY                                    = 0x0
	O_RDWR                                      = 0x2
	O_WRONLY                                    = 0x1
	PACKET_ADD_MEMBERSHIP                       = 0x1
	PACKET_AUXDATA                              = 0x8
	PACKET_BROADCAST                            = 0x1
	PACKET_COPY_THRESH                          = 0x7
	PACKET_DROP_MEMBERSHIP                      = 0x2
	PACKET_FANOUT                               = 0x12
	PACKET_FANOUT_CBPF                          = 0x6
	PACKET_FANOUT_CPU                           = 0x2
	PACKET_FANOUT_DATA                          = 0x16
	PACKET_FANOUT_EBPF                          = 0x7
	PACKET_FANOUT_FLAG_DEFRAG                   = 0x8000
	PACKET_FANOUT_FLAG_IGNORE_OUTGOING          = 0x4000
	PACKET_FANOUT_FLAG_ROLLOVER                 = 0x1000
	PACKET_FANOUT_FLAG_UNIQUEID                 = 0x2000
	PACKET_FANOUT_HASH                          = 0x0
	PACKET_FANOUT_LB                            = 0x1
	PACKET_FANOUT_QM                            = 0x5
	PACKET_FANOUT_RND                           = 0x4
	PACKET_FANOUT_ROLLOVER                      = 0x3
	PACKET_FASTROUTE                            = 0x6
	PACKET_HDRLEN                               = 0xb
	PACKET_HOST                                 = 0x0
	PACKET_IGNORE_OUTGOING                      = 0x17
	PACKET_KERNEL                               = 0x7
	PACKET_LOOPBACK                             = 0x5
	PACKET_LOSS                                 = 0xe
	PACKET_MR_ALLMULTI                          = 0x2
	PACKET_MR_MULTICAST                         = 0x0
	PACKET_MR_PROMISC                           = 0x1
	PACKET_MR_UNICAST                           = 0x3
	PACKET_MULTICAST                            = 0x2
	PACKET_ORIGDEV                              = 0x9
	PACKET_OTHERHOST                            = 0x3
	PACKET_OUTGOING                             = 0x4
	PACKET_QDISC_BYPASS                         = 0x14
	PACKET_RECV_OUTPUT                          = 0x3
	PACKET_RESERVE                              = 0xc
	PACKET_ROLLOVER_STATS                       = 0x15
	PACKET_RX_RING                              = 0x5
	PACKET_STATISTICS                           = 0x6
	PACKET_TIMESTAMP                            = 0x11
	PACKET_TX_HAS_OFF                           = 0x13
	PACKET_TX_RING                              = 0xd
	PACKET_TX_TIMESTAMP                         = 0x10
	PACKET_USER                                 = 0x6
	PACKET_VERSION                              = 0xa
	PACKET_VNET_HDR                             = 0xf
	PACKET_VNET_HDR_SZ                          = 0x18
	PARITY_CRC16_PR0                            = 0x2
	PARITY_CRC16_PR0_CCITT                      = 0x4
	PARITY_CRC16_PR1                            = 0x3
	PARITY_CRC16_PR1_CCITT                      = 0x5
	PARITY_CRC32_PR0_CCITT                      = 0x6
	PARITY_CRC32_PR1_CCITT                      = 0x7
	PARITY_DEFAULT                              = 0x0
	PARITY_NONE                                 = 0x1
	PARMRK                                      = 0x8
	PERF_ATTR_SIZE_VER0                         = 0x40
	PERF_ATTR_SIZE_VER1                         = 0x48
	PERF_ATTR_SIZE_VER2                         = 0x50
	PERF_ATTR_SIZE_VER3                         = 0x60
	PERF_ATTR_SIZE_VER4                         = 0x68
	PERF_ATTR_SIZE_VER5                         = 0x70
	PERF_ATTR_SIZE_VER6                         = 0x78
	PERF_ATTR_SIZE_VER7                         = 0x80
	PERF_ATTR_SIZE_VER8                         = 0x88
	PERF_AUX_FLAG_COLLISION                     = 0x8
	PERF_AUX_FLAG_CORESIGHT_FORMAT_CORESIGHT    = 0x0
	PERF_AUX_FLAG_CORESIGHT_FORMAT_RAW          = 0x100
	PERF_AUX_FLAG_OVERWRITE                     = 0x2
	PERF_AUX_FLAG_PARTIAL                       = 0x4
	PERF_AUX_FLAG_PMU_FORMAT_TYPE_MASK          = 0xff00
	PERF_AUX_FLAG_TRUNCATED                     = 0x1
	PERF_BRANCH_ENTRY_INFO_BITS_MAX             = 0x21
	PERF_BR_ARM64_DEBUG_DATA                    = 0x7
	PERF_BR_ARM64_DEBUG_EXIT                    = 0x5
	PERF_BR_ARM64_DEBUG_HALT                    = 0x4
	PERF_BR_ARM64_DEBUG_INST                    = 0x6
	PERF_BR_ARM64_FIQ                           = 0x3
	PERF_FLAG_FD_CLOEXEC                        = 0x8
	PERF_FLAG_FD_NO_GROUP                       = 0x1
	PERF_FLAG_FD_OUTPUT                         = 0x2
	PERF_FLAG_PID_CGROUP                        = 0x4
	PERF_HW_EVENT_MASK                          = 0xffffffff
	PERF_MAX_CONTEXTS_PER_STACK                 = 0x8
	PERF_MAX_STACK_DEPTH                        = 0x7f
	PERF_MEM_BLK_ADDR                           = 0x4
	PERF_MEM_BLK_DATA                           = 0x2
	PERF_MEM_BLK_NA                             = 0x1
	PERF_MEM_BLK_SHIFT                          = 0x28
	PERF_MEM_HOPS_0                             = 0x1
	PERF_MEM_HOPS_1                             = 0x2
	PERF_MEM_HOPS_2                             = 0x3
	PERF_MEM_HOPS_3                             = 0x4
	PERF_MEM_HOPS_SHIFT                         = 0x2b
	PERF_MEM_LOCK_LOCKED                        = 0x2
	PERF_MEM_LOCK_NA                            = 0x1
	PERF_MEM_LOCK_SHIFT                         = 0x18
	PERF_MEM_LVLNUM_ANY_CACHE                   = 0xb
	PERF_MEM_LVLNUM_CXL                         = 0x9
	PERF_MEM_LVLNUM_IO                          = 0xa
	PERF_MEM_LVLNUM_L1                          = 0x1
	PERF_MEM_LVLNUM_L2                          = 0x2
	PERF_MEM_LVLNUM_L3                          = 0x3
	PERF_MEM_LVLNUM_L4                          = 0x4
	PERF_MEM_LVLNUM_LFB                         = 0xc
	PERF_MEM_LVLNUM_NA                          = 0xf
	PERF_MEM_LVLNUM_PMEM                        = 0xe
	PERF_MEM_LVLNUM_RAM                         = 0xd
	PERF_MEM_LVLNUM_SHIFT                       = 0x21
	PERF_MEM_LVLNUM_UNC                         = 0x8
	PERF_MEM_LVL_HIT                            = 0x2
	PERF_MEM_LVL_IO                             = 0x1000
	PERF_MEM_LVL_L1                             = 0x8
	PERF_MEM_LVL_L2                             = 0x20
	PERF_MEM_LVL_L3                             = 0x40
	PERF_MEM_LVL_LFB                            = 0x10
	PERF_MEM_LVL_LOC_RAM                        = 0x80
	PERF_MEM_LVL_MISS                           = 0x4
	PERF_MEM_LVL_NA                             = 0x1
	PERF_MEM_LVL_REM_CCE1                       = 0x400
	PERF_MEM_LVL_REM_CCE2                       = 0x800
	PERF_MEM_LVL_REM_RAM1                       = 0x100
	PERF_MEM_LVL_REM_RAM2                       = 0x200
	PERF_MEM_LVL_SHIFT                          = 0x5
	PERF_MEM_LVL_UNC                            = 0x2000
	PERF_MEM_OP_EXEC                            = 0x10
	PERF_MEM_OP_LOAD                            = 0x2
	PERF_MEM_OP_NA                              = 0x1
	PERF_MEM_OP_PFETCH                          = 0x8
	PERF_MEM_OP_SHIFT                           = 0x0
	PERF_MEM_OP_STORE                           = 0x4
	PERF_MEM_REMOTE_REMOTE                      = 0x1
	PERF_MEM_REMOTE_SHIFT                       = 0x25
	PERF_MEM_SNOOPX_FWD                         = 0x1
	PERF_MEM_SNOOPX_PEER                        = 0x2
	PERF_MEM_SNOOPX_SHIFT                       = 0x26
	PERF_MEM_SNOOP_HIT                          = 0x4
	PERF_MEM_SNOOP_HITM                         = 0x10
	PERF_MEM_SNOOP_MISS                         = 0x8
	PERF_MEM_SNOOP_NA                           = 0x1
	PERF_MEM_SNOOP_NONE                         = 0x2
	PERF_MEM_SNOOP_SHIFT                        = 0x13
	PERF_MEM_TLB_HIT                            = 0x2
	PERF_MEM_TLB_L1                             = 0x8
	PERF_MEM_TLB_L2                             = 0x10
	PERF_MEM_TLB_MISS                           = 0x4
	PERF_MEM_TLB_NA                             = 0x1
	PERF_MEM_TLB_OS                             = 0x40
	PERF_MEM_TLB_SHIFT                          = 0x1a
	PERF_MEM_TLB_WK                             = 0x20
	PERF_PMU_TYPE_SHIFT                         = 0x20
	PERF_RECORD_KSYMBOL_FLAGS_UNREGISTER        = 0x1
	PERF_RECORD_MISC_COMM_EXEC                  = 0x2000
	PERF_RECORD_MISC_CPUMODE_MASK               = 0x7
	PERF_RECORD_MISC_CPUMODE_UNKNOWN            = 0x0
	PERF_RECORD_MISC_EXACT_IP                   = 0x4000
	PERF_RECORD_MISC_EXT_RESERVED               = 0x8000
	PERF_RECORD_MISC_FORK_EXEC                  = 0x2000
	PERF_RECORD_MISC_GUEST_KERNEL               = 0x4
	PERF_RECORD_MISC_GUEST_USER                 = 0x5
	PERF_RECORD_MISC_HYPERVISOR                 = 0x3
	PERF_RECORD_MISC_KERNEL                     = 0x1
	PERF_RECORD_MISC_MMAP_BUILD_ID              = 0x4000
	PERF_RECORD_MISC_MMAP_DATA                  = 0x2000
	PERF_RECORD_MISC_PROC_MAP_PARSE_TIMEOUT     = 0x1000
	PERF_RECORD_MISC_SWITCH_OUT                 = 0x2000
	PERF_RECORD_MISC_SWITCH_OUT_PREEMPT         = 0x4000
	PERF_RECORD_MISC_USER                       = 0x2
	PERF_SAMPLE_BRANCH_PLM_ALL                  = 0x7
	PERF_SAMPLE_WEIGHT_TYPE                     = 0x1004000
	PIPEFS_MAGIC                                = 0x50495045
	PPPIOCGNPMODE                               = 0xc008744c
	PPPIOCNEWUNIT                               = 0xc004743e
	PRIO_PGRP                                   = 0x1
	PRIO_PROCESS                                = 0x0
	PRIO_USER                                   = 0x2
	PROC_SUPER_MAGIC                            = 0x9fa0
	PROT_EXEC                                   = 0x4
	PROT_GROWSDOWN                              = 0x1000000
	PROT_GROWSUP                                = 0x2000000
	PROT_NONE                                   = 0x0
	PROT_READ                                   = 0x1
	PROT_WRITE                                  = 0x2
	PR_CAPBSET_DROP                             = 0x18
	PR_CAPBSET_READ                             = 0x17
	PR_CAP_AMBIENT                              = 0x2f
	PR_CAP_AMBIENT_CLEAR_ALL                    = 0x4
	PR_CAP_AMBIENT_IS_SET                       = 0x1
	PR_CAP_AMBIENT_LOWER                        = 0x3
	PR_CAP_AMBIENT_RAISE                        = 0x2
	PR_ENDIAN_BIG                               = 0x0
	PR_ENDIAN_LITTLE                            = 0x1
	PR_ENDIAN_PPC_LITTLE                        = 0x2
	PR_FPEMU_NOPRINT                            = 0x1
	PR_FPEMU_SIGFPE                             = 0x2
	PR_FP_EXC_ASYNC                             = 0x2
	PR_FP_EXC_DISABLED                          = 0x0
	PR_FP_EXC_DIV                               = 0x10000
	PR_FP_EXC_INV                               = 0x100000
	PR_FP_EXC_NONRECOV                          = 0x1
	PR_FP_EXC_OVF                               = 0x20000
	PR_FP_EXC_PRECISE                           = 0x3
	PR_FP_EXC_RES                               = 0x80000
	PR_FP_EXC_SW_ENABLE                         = 0x80
	PR_FP_EXC_UND                               = 0x40000
	PR_FP_MODE_FR                               = 0x1
	PR_FP_MODE_FRE                              = 0x2
	PR_GET_AUXV                                 = 0x41555856
	PR_GET_CHILD_SUBREAPER                      = 0x25
	PR_GET_DUMPABLE                             = 0x3
	PR_GET_ENDIAN                               = 0x13
	PR_GET_FPEMU                                = 0x9
	PR_GET_FPEXC                                = 0xb
	PR_GET_FP_MODE                              = 0x2e
	PR_GET_IO_FLUSHER                           = 0x3a
	PR_GET_KEEPCAPS                             = 0x7
	PR_GET_MDWE                                 = 0x42
	PR_GET_MEMORY_MERGE                         = 0x44
	PR_GET_NAME                                 = 0x10
	PR_GET_NO_NEW_PRIVS                         = 0x27
	PR_GET_PDEATHSIG                            = 0x2
	PR_GET_SECCOMP                              = 0x15
	PR_GET_SECUREBITS                           = 0x1b
	PR_GET_SPECULATION_CTRL                     = 0x34
	PR_GET_TAGGED_ADDR_CTRL                     = 0x38
	PR_GET_THP_DISABLE                          = 0x2a
	PR_GET_TID_ADDRESS                          = 0x28
	PR_GET_TIMERSLACK                           = 0x1e
	PR_GET_TIMING                               = 0xd
	PR_GET_TSC                                  = 0x19
	PR_GET_UNALIGN                              = 0x5
	PR_MCE_KILL                                 = 0x21
	PR_MCE_KILL_CLEAR                           = 0x0
	PR_MCE_KILL_DEFAULT                         = 0x2
	PR_MCE_KILL_EARLY                           = 0x1
	PR_MCE_KILL_GET                             = 0x22
	PR_MCE_KILL_LATE                            = 0x0
	PR_MCE_KILL_SET                             = 0x1
	PR_MDWE_NO_INHERIT                          = 0x2
	PR_MDWE_REFUSE_EXEC_GAIN                    = 0x1
	PR_MPX_DISABLE_MANAGEMENT                   = 0x2c
	PR_MPX_ENABLE_MANAGEMENT                    = 0x2b
	PR_MTE_TAG_MASK                             = 0x7fff8
	PR_MTE_TAG_SHIFT                            = 0x3
	PR_MTE_TCF_ASYNC                            = 0x4
	PR_MTE_TCF_MASK                             = 0x6
	PR_MTE_TCF_NONE                             = 0x0
	PR_MTE_TCF_SHIFT                            = 0x1
	PR_MTE_TCF_SYNC                             = 0x2
	PR_PAC_APDAKEY                              = 0x4
	PR_PAC_APDBKEY                              = 0x8
	PR_PAC_APGAKEY                              = 0x10
	PR_PAC_APIAKEY                              = 0x1
	PR_PAC_APIBKEY                              = 0x2
	PR_PAC_GET_ENABLED_KEYS                     = 0x3d
	PR_PAC_RESET_KEYS                           = 0x36
	PR_PAC_SET_ENABLED_KEYS                     = 0x3c
	PR_RISCV_V_GET_CONTROL                      = 0x46
	PR_RISCV_V_SET_CONTROL                      = 0x45
	PR_RISCV_V_VSTATE_CTRL_CUR_MASK             = 0x3
	PR_RISCV_V_VSTATE_CTRL_DEFAULT              = 0x0
	PR_RISCV_V_VSTATE_CTRL_INHERIT              = 0x10
	PR_RISCV_V_VSTATE_CTRL_MASK                 = 0x1f
	PR_RISCV_V_VSTATE_CTRL_NEXT_MASK            = 0xc
	PR_RISCV_V_VSTATE_CTRL_OFF                  = 0x1
	PR_RISCV_V_VSTATE_CTRL_ON                   = 0x2
	PR_SCHED_CORE                               = 0x3e
	PR_SCHED_CORE_CREATE                        = 0x1
	PR_SCHED_CORE_GET                           = 0x0
	PR_SCHED_CORE_MAX                           = 0x4
	PR_SCHED_CORE_SCOPE_PROCESS_GROUP           = 0x2
	PR_SCHED_CORE_SCOPE_THREAD                  = 0x0
	PR_SCHED_CORE_SCOPE_THREAD_GROUP            = 0x1
	PR_SCHED_CORE_SHARE_FROM                    = 0x3
	PR_SCHED_CORE_SHARE_TO                      = 0x2
	PR_SET_CHILD_SUBREAPER                      = 0x24
	PR_SET_DUMPABLE                             = 0x4
	PR_SET_ENDIAN                               = 0x14
	PR_SET_FPEMU                                = 0xa
	PR_SET_FPEXC                                = 0xc
	PR_SET_FP_MODE                              = 0x2d
	PR_SET_IO_FLUSHER                           = 0x39
	PR_SET_KEEPCAPS                             = 0x8
	PR_SET_MDWE                                 = 0x41
	PR_SET_MEMORY_MERGE                         = 0x43
	PR_SET_MM                                   = 0x23
	PR_SET_MM_ARG_END                           = 0x9
	PR_SET_MM_ARG_START                         = 0x8
	PR_SET_MM_AUXV                              = 0xc
	PR_SET_MM_BRK                               = 0x7
	PR_SET_MM_END_CODE                          = 0x2
	PR_SET_MM_END_DATA                          = 0x4
	PR_SET_MM_ENV_END                           = 0xb
	PR_SET_MM_ENV_START                         = 0xa
	PR_SET_MM_EXE_FILE                          = 0xd
	PR_SET_MM_MAP                               = 0xe
	PR_SET_MM_MAP_SIZE                          = 0xf
	PR_SET_MM_START_BRK                         = 0x6
	PR_SET_MM_START_CODE                        = 0x1
	PR_SET_MM_START_DATA                        = 0x3
	PR_SET_MM_START_STACK                       = 0x5
	PR_SET_NAME                                 = 0xf
	PR_SET_NO_NEW_PRIVS                         = 0x26
	PR_SET_PDEATHSIG                            = 0x1
	PR_SET_PTRACER                              = 0x59616d61
	PR_SET_SECCOMP                              = 0x16
	PR_SET_SECUREBITS                           = 0x1c
	PR_SET_SPECULATION_CTRL                     = 0x35
	PR_SET_SYSCALL_USER_DISPATCH                = 0x3b
	PR_SET_TAGGED_ADDR_CTRL                     = 0x37
	PR_SET_THP_DISABLE                          = 0x29
	PR_SET_TIMERSLACK                           = 0x1d
	PR_SET_TIMING                               = 0xe
	PR_SET_TSC                                  = 0x1a
	PR_SET_UNALIGN                              = 0x6
	PR_SET_VMA                                  = 0x53564d41
	PR_SET_VMA_ANON_NAME                        = 0x0
	PR_SME_GET_VL                               = 0x40
	PR_SME_SET_VL                               = 0x3f
	PR_SME_SET_VL_ONEXEC                        = 0x40000
	PR_SME_VL_INHERIT                           = 0x20000
	PR_SME_VL_LEN_MASK                          = 0xffff
	PR_SPEC_DISABLE                             = 0x4
	PR_SPEC_DISABLE_NOEXEC                      = 0x10
	PR_SPEC_ENABLE                              = 0x2
	PR_SPEC_FORCE_DISABLE                       = 0x8
	PR_SPEC_INDIRECT_BRANCH                     = 0x1
	PR_SPEC_L1D_FLUSH                           = 0x2
	PR_SPEC_NOT_AFFECTED                        = 0x0
	PR_SPEC_PRCTL                               = 0x1
	PR_SPEC_STORE_BYPASS                        = 0x0
	PR_SVE_GET_VL                               = 0x33
	PR_SVE_SET_VL                               = 0x32
	PR_SVE_SET_VL_ONEXEC                        = 0x40000
	PR_SVE_VL_INHERIT                           = 0x20000
	PR_SVE_VL_LEN_MASK                          = 0xffff
	PR_SYS_DISPATCH_OFF                         = 0x0
	PR_SYS_DISPATCH_ON                          = 0x1
	PR_TAGGED_ADDR_ENABLE                       = 0x1
	PR_TASK_PERF_EVENTS_DISABLE                 = 0x1f
	PR_TASK_PERF_EVENTS_ENABLE                  = 0x20
	PR_TIMING_STATISTICAL                       = 0x0
	PR_TIMING_TIMESTAMP                         = 0x1
	PR_TSC_ENABLE                               = 0x1
	PR_TSC_SIGSEGV                              = 0x2
	PR_UNALIGN_NOPRINT                          = 0x1
	PR_UNALIGN_SIGBUS                           = 0x2
	PSTOREFS_MAGIC                              = 0x6165676c
	PTRACE_ATTACH                               = 0x10
	PTRACE_CONT                                 = 0x7
	PTRACE_DETACH                               = 0x11
	PTRACE_EVENTMSG_SYSCALL_ENTRY               = 0x1
	PTRACE_EVENTMSG_SYSCALL_EXIT                = 0x2
	PTRACE_EVENT_CLONE                          = 0x3
	PTRACE_EVENT_EXEC                           = 0x4
	PTRACE_EVENT_EXIT                           = 0x6
	PTRACE_EVENT_FORK                           = 0x1
	PTRACE_EVENT_SECCOMP                        = 0x7
	PTRACE_EVENT_STOP                           = 0x80
	PTRACE_EVENT_VFORK                          = 0x2
	PTRACE_EVENT_VFORK_DONE                     = 0x5
	PTRACE_GETEVENTMSG                          = 0x4201
	PTRACE_GETREGS                              = 0xc
	PTRACE_GETREGSET                            = 0x4204
	PTRACE_GETSIGINFO                           = 0x4202
	PTRACE_GETSIGMASK                           = 0x420a
	PTRACE_GET_RSEQ_CONFIGURATION               = 0x420f
	PTRACE_GET_SYSCALL_INFO                     = 0x420e
	PTRACE_GET_SYSCALL_USER_DISPATCH_CONFIG     = 0x4211
	PTRACE_INTERRUPT                            = 0x4207
	PTRACE_KILL                                 = 0x8
	PTRACE_LISTEN                               = 0x4208
	PTRACE_O_EXITKILL                           = 0x100000
	PTRACE_O_MASK                               = 0x3000ff
	PTRACE_O_SUSPEND_SECCOMP                    = 0x200000
	PTRACE_O_TRACECLONE                         = 0x8
	PTRACE_O_TRACEEXEC                          = 0x10
	PTRACE_O_TRACEEXIT                          = 0x40
	PTRACE_O_TRACEFORK                          = 0x2
	PTRACE_O_TRACESECCOMP                       = 0x80
	PTRACE_O_TRACESYSGOOD                       = 0x1
	PTRACE_O_TRACEVFORK                         = 0x4
	PTRACE_O_TRACEVFORKDONE                     = 0x20
	PTRACE_PEEKDATA                             = 0x2
	PTRACE_PEEKSIGINFO                          = 0x4209
	PTRACE_PEEKSIGINFO_SHARED                   = 0x1
	PTRACE_PEEKTEXT                             = 0x1
	PTRACE_PEEKUSR                              = 0x3
	PTRACE_POKEDATA                             = 0x5
	PTRACE_POKETEXT                             = 0x4
	PTRACE_POKEUSR                              = 0x6
	PTRACE_SECCOMP_GET_FILTER                   = 0x420c
	PTRACE_SECCOMP_GET_METADATA                 = 0x420d
	PTRACE_SEIZE                                = 0x4206
	PTRACE_SETOPTIONS                           = 0x4200
	PTRACE_SETREGS                              = 0xd
	PTRACE_SETREGSET                            = 0x4205
	PTRACE_SETSIGINFO                           = 0x4203
	PTRACE_SETSIGMASK                           = 0x420b
	PTRACE_SET_SYSCALL_USER_DISPATCH_CONFIG     = 0x4210
	PTRACE_SINGLESTEP                           = 0x9
	PTRACE_SYSCALL                              = 0x18
	PTRACE_SYSCALL_INFO_ENTRY                   = 0x1
	PTRACE_SYSCALL_INFO_EXIT                    = 0x2
	PTRACE_SYSCALL_INFO_NONE                    = 0x0
	PTRACE_SYSCALL_INFO_SECCOMP                 = 0x3
	PTRACE_TRACEME                              = 0x0
	P_ALL                                       = 0x0
	P_PGID                                      = 0x2
	P_PID                                       = 0x1
	P_PIDFD                                     = 0x3
	QNX4_SUPER_MAGIC                            = 0x2f
	QNX6_SUPER_MAGIC                            = 0x68191122
	RAMFS_MAGIC                                 = 0x858458f6
	RAW_PAYLOAD_DIGITAL                         = 0x3
	RAW_PAYLOAD_HCI                             = 0x2
	RAW_PAYLOAD_LLCP                            = 0x0
	RAW_PAYLOAD_NCI                             = 0x1
	RAW_PAYLOAD_PROPRIETARY                     = 0x4
	RDTGROUP_SUPER_MAGIC                        = 0x7655821
	REISERFS_SUPER_MAGIC                        = 0x52654973
	RENAME_EXCHANGE                             = 0x2
	RENAME_NOREPLACE                            = 0x1
	RENAME_WHITEOUT                             = 0x4
	RLIMIT_CORE                                 = 0x4
	RLIMIT_CPU                                  = 0x0
	RLIMIT_DATA                                 = 0x2
	RLIMIT_FSIZE                                = 0x1
	RLIMIT_LOCKS                                = 0xa
	RLIMIT_MSGQUEUE                             = 0xc
	RLIMIT_NICE                                 = 0xd
	RLIMIT_RTPRIO                               = 0xe
	RLIMIT_RTTIME                               = 0xf
	RLIMIT_SIGPENDING                           = 0xb
	RLIMIT_STACK                                = 0x3
	RLIM_INFINITY                               = 0xffffffffffffffff
	RTAX_ADVMSS                                 = 0x8
	RTAX_CC_ALGO                                = 0x10
	RTAX_CWND                                   = 0x7
	RTAX_FASTOPEN_NO_COOKIE                     = 0x11
	RTAX_FEATURES                               = 0xc
	RTAX_FEATURE_ALLFRAG                        = 0x8
	RTAX_FEATURE_ECN                            = 0x1
	RTAX_FEATURE_MASK                           = 0x1f
	RTAX_FEATURE_SACK                           = 0x2
	RTAX_FEATURE_TCP_USEC_TS                    = 0x10
	RTAX_FEATURE_TIMESTAMP                      = 0x4
	RTAX_HOPLIMIT                               = 0xa
	RTAX_INITCWND                               = 0xb
	RTAX_INITRWND                               = 0xe
	RTAX_LOCK                                   = 0x1
	RTAX_MAX                                    = 0x11
	RTAX_MTU                                    = 0x2
	RTAX_QUICKACK                               = 0xf
	RTAX_REORDERING                             = 0x9
	RTAX_RTO_MIN                                = 0xd
	RTAX_RTT                                    = 0x4
	RTAX_RTTVAR                                 = 0x5
	RTAX_SSTHRESH                               = 0x6
	RTAX_UNSPEC                                 = 0x0
	RTAX_WINDOW                                 = 0x3
	RTA_ALIGNTO                                 = 0x4
	RTA_MAX                                     = 0x1e
	RTCF_DIRECTSRC                              = 0x4000000
	RTCF_DOREDIRECT                             = 0x1000000
	RTCF_LOG                                    = 0x2000000
	RTCF_MASQ                                   = 0x400000
	RTCF_NAT                                    = 0x800000
	RTCF_VALVE                                  = 0x200000
	RTC_AF                                      = 0x20
	RTC_BSM_DIRECT                              = 0x1
	RTC_BSM_DISABLED                            = 0x0
	RTC_BSM_LEVEL                               = 0x2
	RTC_BSM_STANDBY                             = 0x3
	RTC_FEATURE_ALARM                           = 0x0
	RTC_FEATURE_ALARM_RES_2S                    = 0x3
	RTC_FEATURE_ALARM_RES_MINUTE                = 0x1
	RTC_FEATURE_ALARM_WAKEUP_ONLY               = 0x7
	RTC_FEATURE_BACKUP_SWITCH_MODE              = 0x6
	RTC_FEATURE_CNT                             = 0x8
	RTC_FEATURE_CORRECTION                      = 0x5
	RTC_FEATURE_NEED_WEEK_DAY                   = 0x2
	RTC_FEATURE_UPDATE_INTERRUPT                = 0x4
	RTC_IRQF                                    = 0x80
	RTC_MAX_FREQ                                = 0x2000
	RTC_PARAM_BACKUP_SWITCH_MODE                = 0x2
	RTC_PARAM_CORRECTION                        = 0x1
	RTC_PARAM_FEATURES                          = 0x0
	RTC_PF                                      = 0x40
	RTC_UF                                      = 0x10
	RTF_ADDRCLASSMASK                           = 0xf8000000
	RTF_ADDRCONF                                = 0x40000
	RTF_ALLONLINK                               = 0x20000
	RTF_BROADCAST                               = 0x10000000
	RTF_CACHE                                   = 0x1000000
	RTF_DEFAULT                                 = 0x10000
	RTF_DYNAMIC                                 = 0x10
	RTF_FLOW                                    = 0x2000000
	RTF_GATEWAY                                 = 0x2
	RTF_HOST                                    = 0x4
	RTF_INTERFACE                               = 0x40000000
	RTF_IRTT                                    = 0x100
	RTF_LINKRT                                  = 0x100000
	RTF_LOCAL                                   = 0x80000000
	RTF_MODIFIED                                = 0x20
	RTF_MSS                                     = 0x40
	RTF_MTU                                     = 0x40
	RTF_MULTICAST                               = 0x20000000
	RTF_NAT                                     = 0x8000000
	RTF_NOFORWARD                               = 0x1000
	RTF_NONEXTHOP                               = 0x200000
	RTF_NOPMTUDISC                              = 0x4000
	RTF_POLICY                                  = 0x4000000
	RTF_REINSTATE                               = 0x8
	RTF_REJECT                                  = 0x200
	RTF_STATIC                                  = 0x400
	RTF_THROW                                   = 0x2000
	RTF_UP                                      = 0x1
	RTF_WINDOW                                  = 0x80
	RTF_XRESOLVE                                = 0x800
	RTMGRP_DECnet_IFADDR                        = 0x1000
	RTMGRP_DECnet_ROUTE                         = 0x4000
	RTMGRP_IPV4_IFADDR                          = 0x10
	RTMGRP_IPV4_MROUTE                          = 0x20
	RTMGRP_IPV4_ROUTE                           = 0x40
	RTMGRP_IPV4_RULE                            = 0x80
	RTMGRP_IPV6_IFADDR                          = 0x100
	RTMGRP_IPV6_IFINFO                          = 0x800
	RTMGRP_IPV6_MROUTE                          = 0x200
	RTMGRP_IPV6_PREFIX                          = 0x20000
	RTMGRP_IPV6_ROUTE                           = 0x400
	RTMGRP_LINK                                 = 0x1
	RTMGRP_NEIGH                                = 0x4
	RTMGRP_NOTIFY                               = 0x2
	RTMGRP_TC                                   = 0x8
	RTM_BASE                                    = 0x10
	RTM_DELACTION                               = 0x31
	RTM_DELADDR                                 = 0x15
	RTM_DELADDRLABEL                            = 0x49
	RTM_DELCHAIN                                = 0x65
	RTM_DELLINK                                 = 0x11
	RTM_DELLINKPROP                             = 0x6d
	RTM_DELMDB                                  = 0x55
	RTM_DELNEIGH                                = 0x1d
	RTM_DELNETCONF                              = 0x51
	RTM_DELNEXTHOP                              = 0x69
	RTM_DELNEXTHOPBUCKET                        = 0x75
	RTM_DELNSID                                 = 0x59
	RTM_DELQDISC                                = 0x25
	RTM_DELROUTE                                = 0x19
	RTM_DELRULE                                 = 0x21
	RTM_DELTCLASS                               = 0x29
	RTM_DELTFILTER                              = 0x2d
	RTM_DELTUNNEL                               = 0x79
	RTM_DELVLAN                                 = 0x71
	RTM_F_CLONED                                = 0x200
	RTM_F_EQUALIZE                              = 0x400
	RTM_F_FIB_MATCH                             = 0x2000
	RTM_F_LOOKUP_TABLE                          = 0x1000
	RTM_F_NOTIFY                                = 0x100
	RTM_F_OFFLOAD                               = 0x4000
	RTM_F_OFFLOAD_FAILED                        = 0x20000000
	RTM_F_PREFIX                                = 0x800
	RTM_F_TRAP                                  = 0x8000
	RTM_GETACTION                               = 0x32
	RTM_GETADDR                                 = 0x16
	RTM_GETADDRLABEL                            = 0x4a
	RTM_GETANYCAST                              = 0x3e
	RTM_GETCHAIN                                = 0x66
	RTM_GETDCB                                  = 0x4e
	RTM_GETLINK                                 = 0x12
	RTM_GETLINKPROP                             = 0x6e
	RTM_GETMDB                                  = 0x56
	RTM_GETMULTICAST                            = 0x3a
	RTM_GETNEIGH                                = 0x1e
	RTM_GETNEIGHTBL                             = 0x42
	RTM_GETNETCONF                              = 0x52
	RTM_GETNEXTHOP                              = 0x6a
	RTM_GETNEXTHOPBUCKET                        = 0x76
	RTM_GETNSID                                 = 0x5a
	RTM_GETQDISC                                = 0x26
	RTM_GETROUTE                                = 0x1a
	RTM_GETRULE                                 = 0x22
	RTM_GETSTATS                                = 0x5e
	RTM_GETTCLASS                               = 0x2a
	RTM_GETTFILTER                              = 0x2e
	RTM_GETTUNNEL                               = 0x7a
	RTM_GETVLAN                                 = 0x72
	RTM_MAX                                     = 0x7b
	RTM_NEWACTION                               = 0x30
	RTM_NEWADDR                                 = 0x14
	RTM_NEWADDRLABEL                            = 0x48
	RTM_NEWCACHEREPORT                          = 0x60
	RTM_NEWCHAIN                                = 0x64
	RTM_NEWLINK                                 = 0x10
	RTM_NEWLINKPROP                             = 0x6c
	RTM_NEWMDB                                  = 0x54
	RTM_NEWNDUSEROPT                            = 0x44
	RTM_NEWNEIGH                                = 0x1c
	RTM_NEWNEIGHTBL                             = 0x40
	RTM_NEWNETCONF                              = 0x50
	RTM_NEWNEXTHOP                              = 0x68
	RTM_NEWNEXTHOPBUCKET                        = 0x74
	RTM_NEWNSID                                 = 0x58
	RTM_NEWNVLAN                                = 0x70
	RTM_NEWPREFIX                               = 0x34
	RTM_NEWQDISC                                = 0x24
	RTM_NEWROUTE                                = 0x18
	RTM_NEWRULE                                 = 0x20
	RTM_NEWSTATS                                = 0x5c
	RTM_NEWTCLASS                               = 0x28
	RTM_NEWTFILTER                              = 0x2c
	RTM_NEWTUNNEL                               = 0x78
	RTM_NR_FAMILIES                             = 0x1b
	RTM_NR_MSGTYPES                             = 0x6c
	RTM_SETDCB                                  = 0x4f
	RTM_SETLINK                                 = 0x13
	RTM_SETNEIGHTBL                             = 0x43
	RTM_SETSTATS                                = 0x5f
	RTNH_ALIGNTO                                = 0x4
	RTNH_COMPARE_MASK                           = 0x59
	RTNH_F_DEAD                                 = 0x1
	RTNH_F_LINKDOWN                             = 0x10
	RTNH_F_OFFLOAD                              = 0x8
	RTNH_F_ONLINK                               = 0x4
	RTNH_F_PERVASIVE                            = 0x2
	RTNH_F_TRAP                                 = 0x40
	RTNH_F_UNRESOLVED                           = 0x20
	RTN_MAX                                     = 0xb
	RTPROT_BABEL                                = 0x2a
	RTPROT_BGP                                  = 0xba
	RTPROT_BIRD                                 = 0xc
	RTPROT_BOOT                                 = 0x3
	RTPROT_DHCP                                 = 0x10
	RTPROT_DNROUTED                             = 0xd
	RTPROT_EIGRP                                = 0xc0
	RTPROT_GATED                                = 0x8
	RTPROT_ISIS                                 = 0xbb
	RTPROT_KEEPALIVED                           = 0x12
	RTPROT_KERNEL                               = 0x2
	RTPROT_MROUTED                              = 0x11
	RTPROT_MRT                                  = 0xa
	RTPROT_NTK                                  = 0xf
	RTPROT_OPENR                                = 0x63
	RTPROT_OSPF                                 = 0xbc
	RTPROT_RA                                   = 0x9
	RTPROT_REDIRECT                             = 0x1
	RTPROT_RIP                                  = 0xbd
	RTPROT_STATIC                               = 0x4
	RTPROT_UNSPEC                               = 0x0
	RTPROT_XORP                                 = 0xe
	RTPROT_ZEBRA                                = 0xb
	RT_CLASS_DEFAULT                            = 0xfd
	RT_CLASS_LOCAL                              = 0xff
	RT_CLASS_MAIN                               = 0xfe
	RT_CLASS_MAX                                = 0xff
	RT_CLASS_UNSPEC                             = 0x0
	RUSAGE_CHILDREN                             = -0x1
	RUSAGE_SELF                                 = 0x0
	RUSAGE_THREAD                               = 0x1
	RWF_APPEND                                  = 0x10
	RWF_DSYNC                                   = 0x2
	RWF_HIPRI                                   = 0x1
	RWF_NOWAIT                                  = 0x8
	RWF_SUPPORTED                               = 0x1f
	RWF_SYNC                                    = 0x4
	RWF_WRITE_LIFE_NOT_SET                      = 0x0
	SCHED_BATCH                                 = 0x3
	SCHED_DEADLINE                              = 0x6
	SCHED_FIFO                                  = 0x1
	SCHED_FLAG_ALL                              = 0x7f
	SCHED_FLAG_DL_OVERRUN                       = 0x4
	SCHED_FLAG_KEEP_ALL                         = 0x18
	SCHED_FLAG_KEEP_PARAMS                      = 0x10
	SCHED_FLAG_KEEP_POLICY                      = 0x8
	SCHED_FLAG_RECLAIM                          = 0x2
	SCHED_FLAG_RESET_ON_FORK                    = 0x1
	SCHED_FLAG_UTIL_CLAMP                       = 0x60
	SCHED_FLAG_UTIL_CLAMP_MAX                   = 0x40
	SCHED_FLAG_UTIL_CLAMP_MIN                   = 0x20
	SCHED_IDLE                                  = 0x5
	SCHED_NORMAL                                = 0x0
	SCHED_RESET_ON_FORK                         = 0x40000000
	SCHED_RR                                    = 0x2
	SCM_CREDENTIALS                             = 0x2
	SCM_RIGHTS                                  = 0x1
	SCM_TIMESTAMP                               = 0x1d
	SC_LOG_FLUSH                                = 0x100000
	SECCOMP_ADDFD_FLAG_SEND                     = 0x2
	SECCOMP_ADDFD_FLAG_SETFD                    = 0x1
	SECCOMP_FILTER_FLAG_LOG                     = 0x2
	SECCOMP_FILTER_FLAG_NEW_LISTENER            = 0x8
	SECCOMP_FILTER_FLAG_SPEC_ALLOW              = 0x4
	SECCOMP_FILTER_FLAG_TSYNC                   = 0x1
	SECCOMP_FILTER_FLAG_TSYNC_ESRCH             = 0x10
	SECCOMP_FILTER_FLAG_WAIT_KILLABLE_RECV      = 0x20
	SECCOMP_GET_ACTION_AVAIL                    = 0x2
	SECCOMP_GET_NOTIF_SIZES                     = 0x3
	SECCOMP_IOCTL_NOTIF_RECV                    = 0xc0502100
	SECCOMP_IOCTL_NOTIF_SEND                    = 0xc0182101
	SECCOMP_IOC_MAGIC                           = '!'
	SECCOMP_MODE_DISABLED                       = 0x0
	SECCOMP_MODE_FILTER                         = 0x2
	SECCOMP_MODE_STRICT                         = 0x1
	SECCOMP_RET_ACTION                          = 0x7fff0000
	SECCOMP_RET_ACTION_FULL                     = 0xffff0000
	SECCOMP_RET_ALLOW                           = 0x7fff0000
	SECCOMP_RET_DATA                            = 0xffff
	SECCOMP_RET_ERRNO                           = 0x50000
	SECCOMP_RET_KILL                            = 0x0
	SECCOMP_RET_KILL_PROCESS                    = 0x80000000
	SECCOMP_RET_KILL_THREAD                     = 0x0
	SECCOMP_RET_LOG                             = 0x7ffc0000
	SECCOMP_RET_TRACE                           = 0x7ff00000
	SECCOMP_RET_TRAP                            = 0x30000
	SECCOMP_RET_USER_NOTIF                      = 0x7fc00000
	SECCOMP_SET_MODE_FILTER                     = 0x1
	SECCOMP_SET_MODE_STRICT                     = 0x0
	SECCOMP_USER_NOTIF_FD_SYNC_WAKE_UP          = 0x1
	SECCOMP_USER_NOTIF_FLAG_CONTINUE            = 0x1
	SECRETMEM_MAGIC                             = 0x5345434d
	SECURITYFS_MAGIC                            = 0x73636673
	SEEK_CUR                                    = 0x1
	SEEK_DATA                                   = 0x3
	SEEK_END                                    = 0x2
	SEEK_HOLE                                   = 0x4
	SEEK_MAX                                    = 0x4
	SEEK_SET                                    = 0x0
	SELINUX_MAGIC                               = 0xf97cff8c
	SHUT_RD                                     = 0x0
	SHUT_RDWR                                   = 0x2
	SHUT_WR                                     = 0x1
	SIOCADDDLCI                                 = 0x8980
	SIOCADDMULTI                                = 0x8931
	SIOCADDRT                                   = 0x890b
	SIOCBONDCHANGEACTIVE                        = 0x8995
	SIOCBONDENSLAVE                             = 0x8990
	SIOCBONDINFOQUERY                           = 0x8994
	SIOCBONDRELEASE                             = 0x8991
	SIOCBONDSETHWADDR                           = 0x8992
	SIOCBONDSLAVEINFOQUERY                      = 0x8993
	SIOCBRADDBR                                 = 0x89a0
	SIOCBRADDIF                                 = 0x89a2
	SIOCBRDELBR                                 = 0x89a1
	SIOCBRDELIF                                 = 0x89a3
	SIOCDARP                                    = 0x8953
	SIOCDELDLCI                                 = 0x8981
	SIOCDELMULTI                                = 0x8932
	SIOCDELRT                                   = 0x890c
	SIOCDEVPRIVATE                              = 0x89f0
	SIOCDIFADDR                                 = 0x8936
	SIOCDRARP                                   = 0x8960
	SIOCETHTOOL                                 = 0x8946
	SIOCGARP                                    = 0x8954
	SIOCGETLINKNAME                             = 0x89e0
	SIOCGETNODEID                               = 0x89e1
	SIOCGHWTSTAMP                               = 0x89b1
	SIOCGIFADDR                                 = 0x8915
	SIOCGIFBR                                   = 0x8940
	SIOCGIFBRDADDR                              = 0x8919
	SIOCGIFCONF                                 = 0x8912
	SIOCGIFCOUNT                                = 0x8938
	SIOCGIFDSTADDR                              = 0x8917
	SIOCGIFENCAP                                = 0x8925
	SIOCGIFFLAGS                                = 0x8913
	SIOCGIFHWADDR                               = 0x8927
	SIOCGIFINDEX                                = 0x8933
	SIOCGIFMAP                                  = 0x8970
	SIOCGIFMEM                                  = 0x891f
	SIOCGIFMETRIC                               = 0x891d
	SIOCGIFMTU                                  = 0x8921
	SIOCGIFNAME                                 = 0x8910
	SIOCGIFNETMASK                              = 0x891b
	SIOCGIFPFLAGS                               = 0x8935
	SIOCGIFSLAVE                                = 0x8929
	SIOCGIFTXQLEN                               = 0x8942
	SIOCGIFVLAN                                 = 0x8982
	SIOCGMIIPHY                                 = 0x8947
	SIOCGMIIREG                                 = 0x8948
	SIOCGPPPCSTATS                              = 0x89f2
	SIOCGPPPSTATS                               = 0x89f0
	SIOCGPPPVER                                 = 0x89f1
	SIOCGRARP                                   = 0x8961
	SIOCGSKNS                                   = 0x894c
	SIOCGSTAMP                                  = 0x8906
	SIOCGSTAMPNS                                = 0x8907
	SIOCGSTAMPNS_OLD                            = 0x8907
	SIOCGSTAMP_OLD                              = 0x8906
	SIOCKCMATTACH                               = 0x89e0
	SIOCKCMCLONE                                = 0x89e2
	SIOCKCMUNATTACH                             = 0x89e1
	SIOCOUTQNSD                                 = 0x894b
	SIOCPROTOPRIVATE                            = 0x89e0
	SIOCRTMSG                                   = 0x890d
	SIOCSARP                                    = 0x8955
	SIOCSHWTSTAMP                               = 0x89b0
	SIOCSIFADDR                                 = 0x8916
	SIOCSIFBR                                   = 0x8941
	SIOCSIFBRDADDR                              = 0x891a
	SIOCSIFDSTADDR                              = 0x8918
	SIOCSIFENCAP                                = 0x8926
	SIOCSIFFLAGS                                = 0x8914
	SIOCSIFHWADDR                               = 0x8924
	SIOCSIFHWBROADCAST                          = 0x8937
	SIOCSIFLINK                                 = 0x8911
	SIOCSIFMAP                                  = 0x8971
	SIOCSIFMEM                                  = 0x8920
	SIOCSIFMETRIC                               = 0x891e
	SIOCSIFMTU                                  = 0x8922
	SIOCSIFNAME                                 = 0x8923
	SIOCSIFNETMASK                              = 0x891c
	SIOCSIFPFLAGS                               = 0x8934
	SIOCSIFSLAVE                                = 0x8930
	SIOCSIFTXQLEN                               = 0x8943
	SIOCSIFVLAN                                 = 0x8983
	SIOCSMIIREG                                 = 0x8949
	SIOCSRARP                                   = 0x8962
	SIOCWANDEV                                  = 0x894a
	SMACK_MAGIC                                 = 0x43415d53
	SMART_AUTOSAVE                              = 0xd2
	SMART_AUTO_OFFLINE                          = 0xdb
	SMART_DISABLE                               = 0xd9
	SMART_ENABLE                                = 0xd8
	SMART_HCYL_PASS                             = 0xc2
	SMART_IMMEDIATE_OFFLINE                     = 0xd4
	SMART_LCYL_PASS                             = 0x4f
	SMART_READ_LOG_SECTOR                       = 0xd5
	SMART_READ_THRESHOLDS                       = 0xd1
	SMART_READ_VALUES                           = 0xd0
	SMART_SAVE                                  = 0xd3
	SMART_STATUS                                = 0xda
	SMART_WRITE_LOG_SECTOR                      = 0xd6
	SMART_WRITE_THRESHOLDS                      = 0xd7
	SMB2_SUPER_MAGIC                            = 0xfe534d42
	SMB_SUPER_MAGIC                             = 0x517b
	SOCKFS_MAGIC                                = 0x534f434b
	SOCK_BUF_LOCK_MASK                          = 0x3
	SOCK_DCCP                                   = 0x6
	SOCK_IOC_TYPE                               = 0x89
	SOCK_PACKET                                 = 0xa
	SOCK_RAW                                    = 0x3
	SOCK_RCVBUF_LOCK                            = 0x2
	SOCK_RDM                                    = 0x4
	SOCK_SEQPACKET                              = 0x5
	SOCK_SNDBUF_LOCK                            = 0x1
	SOCK_TXREHASH_DEFAULT                       = 0xff
	SOCK_TXREHASH_DISABLED                      = 0x0
	SOCK_TXREHASH_ENABLED                       = 0x1
	SOL_AAL                                     = 0x109
	SOL_ALG                                     = 0x117
	SOL_ATM                                     = 0x108
	SOL_CAIF                                    = 0x116
	SOL_CAN_BASE                                = 0x64
	SOL_CAN_RAW                                 = 0x65
	SOL_DCCP                                    = 0x10d
	SOL_DECNET                                  = 0x105
	SOL_ICMPV6                                  = 0x3a
	SOL_IP                                      = 0x0
	SOL_IPV6                                    = 0x29
	SOL_IRDA                                    = 0x10a
	SOL_IUCV                                    = 0x115
	SOL_KCM                                     = 0x119
	SOL_LLC                                     = 0x10c
	SOL_MCTP                                    = 0x11d
	SOL_MPTCP                                   = 0x11c
	SOL_NETBEUI                                 = 0x10b
	SOL_NETLINK                                 = 0x10e
	SOL_NFC                                     = 0x118
	SOL_PACKET                                  = 0x107
	SOL_PNPIPE                                  = 0x113
	SOL_PPPOL2TP                                = 0x111
	SOL_RAW                                     = 0xff
	SOL_RDS                                     = 0x114
	SOL_RXRPC                                   = 0x110
	SOL_SMC                                     = 0x11e
	SOL_TCP                                     = 0x6
	SOL_TIPC                                    = 0x10f
	SOL_TLS                                     = 0x11a
	SOL_UDP                                     = 0x11
	SOL_VSOCK                                   = 0x11f
	SOL_X25                                     = 0x106
	SOL_XDP                                     = 0x11b
	SOMAXCONN                                   = 0x1000
	SO_ATTACH_FILTER                            = 0x1a
	SO_DEBUG                                    = 0x1
	SO_DETACH_BPF                               = 0x1b
	SO_DETACH_FILTER                            = 0x1b
	SO_EE_CODE_TXTIME_INVALID_PARAM             = 0x1
	SO_EE_CODE_TXTIME_MISSED                    = 0x2
	SO_EE_CODE_ZEROCOPY_COPIED                  = 0x1
	SO_EE_ORIGIN_ICMP                           = 0x2
	SO_EE_ORIGIN_ICMP6                          = 0x3
	SO_EE_ORIGIN_LOCAL                          = 0x1
	SO_EE_ORIGIN_NONE                           = 0x0
	SO_EE_ORIGIN_TIMESTAMPING                   = 0x4
	SO_EE_ORIGIN_TXSTATUS                       = 0x4
	SO_EE_ORIGIN_TXTIME                         = 0x6
	SO_EE_ORIGIN_ZEROCOPY                       = 0x5
	SO_EE_RFC4884_FLAG_INVALID                  = 0x1
	SO_GET_FILTER                               = 0x1a
	SO_NO_CHECK                                 = 0xb
	SO_PEERNAME                                 = 0x1c
	SO_PRIORITY                                 = 0xc
	SO_TIMESTAMP                                = 0x1d
	SO_TIMESTAMP_OLD                            = 0x1d
	SO_VM_SOCKETS_BUFFER_MAX_SIZE               = 0x2
	SO_VM_SOCKETS_BUFFER_MIN_SIZE               = 0x1
	SO_VM_SOCKETS_BUFFER_SIZE                   = 0x0
	SO_VM_SOCKETS_CONNECT_TIMEOUT               = 0x6
	SO_VM_SOCKETS_CONNECT_TIMEOUT_NEW           = 0x8
	SO_VM_SOCKETS_CONNECT_TIMEOUT_OLD           = 0x6
	SO_VM_SOCKETS_NONBLOCK_TXRX                 = 0x7
	SO_VM_SOCKETS_PEER_HOST_VM_ID               = 0x3
	SO_VM_SOCKETS_TRUSTED                       = 0x5
	SPLICE_F_GIFT                               = 0x8
	SPLICE_F_MORE                               = 0x4
	SPLICE_F_MOVE                               = 0x1
	SPLICE_F_NONBLOCK                           = 0x2
	SQUASHFS_MAGIC                              = 0x73717368
	STACK_END_MAGIC                             = 0x57ac6e9d
	STATX_ALL                                   = 0xfff
	STATX_ATIME                                 = 0x20
	STATX_ATTR_APPEND                           = 0x20
	STATX_ATTR_AUTOMOUNT                        = 0x1000
	STATX_ATTR_COMPRESSED                       = 0x4
	STATX_ATTR_DAX                              = 0x200000
	STATX_ATTR_ENCRYPTED                        = 0x800
	STATX_ATTR_IMMUTABLE                        = 0x10
	STATX_ATTR_MOUNT_ROOT                       = 0x2000
	STATX_ATTR_NODUMP                           = 0x40
	STATX_ATTR_VERITY                           = 0x100000
	STATX_BASIC_STATS                           = 0x7ff
	STATX_BLOCKS                                = 0x400
	STATX_BTIME                                 = 0x800
	STATX_CTIME                                 = 0x80
	STATX_DIOALIGN                              = 0x2000
	STATX_GID                                   = 0x10
	STATX_INO                                   = 0x100
	STATX_MNT_ID                                = 0x1000
	STATX_MNT_ID_UNIQUE                         = 0x4000
	STATX_MODE                                  = 0x2
	STATX_MTIME                                 = 0x40
	STATX_NLINK                                 = 0x4
	STATX_SIZE                                  = 0x200
	STATX_TYPE                                  = 0x1
	STATX_UID                                   = 0x8
	STATX__RESERVED                             = 0x80000000
	SYNC_FILE_RANGE_WAIT_AFTER                  = 0x4
	SYNC_FILE_RANGE_WAIT_BEFORE                 = 0x1
	SYNC_FILE_RANGE_WRITE                       = 0x2
	SYNC_FILE_RANGE_WRITE_AND_WAIT              = 0x7
	SYSFS_MAGIC                                 = 0x62656572
	S_BLKSIZE                                   = 0x200
	S_IEXEC                                     = 0x40
	S_IFBLK                                     = 0x6000
	S_IFCHR                                     = 0x2000
	S_IFDIR                                     = 0x4000
	S_IFIFO                                     = 0x1000
	S_IFLNK                                     = 0xa000
	S_IFMT                                      = 0xf000
	S_IFREG                                     = 0x8000
	S_IFSOCK                                    = 0xc000
	S_IREAD                                     = 0x100
	S_IRGRP                                     = 0x20
	S_IROTH                                     = 0x4
	S_IRUSR                                     = 0x100
	S_IRWXG                                     = 0x38
	S_IRWXO                                     = 0x7
	S_IRWXU                                     = 0x1c0
	S_ISGID                                     = 0x400
	S_ISUID                                     = 0x800
	S_ISVTX                                     = 0x200
	S_IWGRP                                     = 0x10
	S_IWOTH                                     = 0x2
	S_IWRITE                                    = 0x80
	S_IWUSR                                     = 0x80
	S_IXGRP                                     = 0x8
	S_IXOTH                                     = 0x1
	S_IXUSR                                     = 0x40
	TAB0                                        = 0x0
	TASKSTATS_CMD_ATTR_MAX                      = 0x4
	TASKSTATS_CMD_MAX                           = 0x2
	TASKSTATS_GENL_NAME                         = "TASKSTATS"
	TASKSTATS_GENL_VERSION                      = 0x1
	TASKSTATS_TYPE_MAX                          = 0x6
	TASKSTATS_VERSION                           = 0xe
	TCIFLUSH                                    = 0x0
	TCIOFF                                      = 0x2
	TCIOFLUSH                                   = 0x2
	TCION                                       = 0x3
	TCOFLUSH                                    = 0x1
	TCOOFF                                      = 0x0
	TCOON                                       = 0x1
	TCPOPT_EOL                                  = 0x0
	TCPOPT_MAXSEG                               = 0x2
	TCPOPT_NOP                                  = 0x1
	TCPOPT_SACK                                 = 0x5
	TCPOPT_SACK_PERMITTED                       = 0x4
	TCPOPT_TIMESTAMP                            = 0x8
	TCPOPT_TSTAMP_HDR                           = 0x101080a
	TCPOPT_WINDOW                               = 0x3
	TCP_CC_INFO                                 = 0x1a
	TCP_CM_INQ                                  = 0x24
	TCP_CONGESTION                              = 0xd
	TCP_COOKIE_IN_ALWAYS                        = 0x1
	TCP_COOKIE_MAX                              = 0x10
	TCP_COOKIE_MIN                              = 0x8
	TCP_COOKIE_OUT_NEVER                        = 0x2
	TCP_COOKIE_PAIR_SIZE                        = 0x20
	TCP_COOKIE_TRANSACTIONS                     = 0xf
	TCP_CORK                                    = 0x3
	TCP_DEFER_ACCEPT                            = 0x9
	TCP_FASTOPEN                                = 0x17
	TCP_FASTOPEN_CONNECT                        = 0x1e
	TCP_FASTOPEN_KEY                            = 0x21
	TCP_FASTOPEN_NO_COOKIE                      = 0x22
	TCP_INFO                                    = 0xb
	TCP_INQ                                     = 0x24
	TCP_KEEPCNT                                 = 0x6
	TCP_KEEPIDLE                                = 0x4
	TCP_KEEPINTVL                               = 0x5
	TCP_LINGER2                                 = 0x8
	TCP_MAXSEG                                  = 0x2
	TCP_MAXWIN                                  = 0xffff
	TCP_MAX_WINSHIFT                            = 0xe
	TCP_MD5SIG                                  = 0xe
	TCP_MD5SIG_EXT                              = 0x20
	TCP_MD5SIG_FLAG_PREFIX                      = 0x1
	TCP_MD5SIG_MAXKEYLEN                        = 0x50
	TCP_MSS                                     = 0x200
	TCP_MSS_DEFAULT                             = 0x218
	TCP_MSS_DESIRED                             = 0x4c4
	TCP_NODELAY                                 = 0x1
	TCP_NOTSENT_LOWAT                           = 0x19
	TCP_QUEUE_SEQ                               = 0x15
	TCP_QUICKACK                                = 0xc
	TCP_REPAIR                                  = 0x13
	TCP_REPAIR_OFF                              = 0x0
	TCP_REPAIR_OFF_NO_WP                        = -0x1
	TCP_REPAIR_ON                               = 0x1
	TCP_REPAIR_OPTIONS                          = 0x16
	TCP_REPAIR_QUEUE                            = 0x14
	TCP_REPAIR_WINDOW                           = 0x1d
	TCP_SAVED_SYN                               = 0x1c
	TCP_SAVE_SYN                                = 0x1b
	TCP_SYNCNT                                  = 0x7
	TCP_S_DATA_IN                               = 0x4
	TCP_S_DATA_OUT                              = 0x8
	TCP_THIN_DUPACK                             = 0x11
	TCP_THIN_LINEAR_TIMEOUTS                    = 0x10
	TCP_TIMESTAMP                               = 0x18
	TCP_TX_DELAY                                = 0x25
	TCP_ULP                                     = 0x1f
	TCP_USER_TIMEOUT                            = 0x12
	TCP_V4_FLOW                                 = 0x1
	TCP_V6_FLOW                                 = 0x5
	TCP_WINDOW_CLAMP                            = 0xa
	TCP_ZEROCOPY_RECEIVE                        = 0x23
	TFD_TIMER_ABSTIME                           = 0x1
	TFD_TIMER_CANCEL_ON_SET                     = 0x2
	TIMER_ABSTIME                               = 0x1
	TIOCM_DTR                                   = 0x2
	TIOCM_LE                                    = 0x1
	TIOCM_RTS                                   = 0x4
	TIOCPKT_DATA                                = 0x0
	TIOCPKT_DOSTOP                              = 0x20
	TIOCPKT_FLUSHREAD                           = 0x1
	TIOCPKT_FLUSHWRITE                          = 0x2
	TIOCPKT_IOCTL                               = 0x40
	TIOCPKT_NOSTOP                              = 0x10
	TIOCPKT_START                               = 0x8
	TIOCPKT_STOP                                = 0x4
	TIPC_ADDR_ID                                = 0x3
	TIPC_ADDR_MCAST                             = 0x1
	TIPC_ADDR_NAME                              = 0x2
	TIPC_ADDR_NAMESEQ                           = 0x1
	TIPC_AEAD_ALG_NAME                          = 0x20
	TIPC_AEAD_KEYLEN_MAX                        = 0x24
	TIPC_AEAD_KEYLEN_MIN                        = 0x14
	TIPC_AEAD_KEY_SIZE_MAX                      = 0x48
	TIPC_CFG_SRV                                = 0x0
	TIPC_CLUSTER_BITS                           = 0xc
	TIPC_CLUSTER_MASK                           = 0xfff000
	TIPC_CLUSTER_OFFSET                         = 0xc
	TIPC_CLUSTER_SIZE                           = 0xfff
	TIPC_CONN_SHUTDOWN                          = 0x5
	TIPC_CONN_TIMEOUT                           = 0x82
	TIPC_CRITICAL_IMPORTANCE                    = 0x3
	TIPC_DESTNAME                               = 0x3
	TIPC_DEST_DROPPABLE                         = 0x81
	TIPC_ERRINFO                                = 0x1
	TIPC_ERR_NO_NAME                            = 0x1
	TIPC_ERR_NO_NODE                            = 0x3
	TIPC_ERR_NO_PORT                            = 0x2
	TIPC_ERR_OVERLOAD                           = 0x4
	TIPC_GROUP_JOIN                             = 0x87
	TIPC_GROUP_LEAVE                            = 0x88
	TIPC_GROUP_LOOPBACK                         = 0x1
	TIPC_GROUP_MEMBER_EVTS                      = 0x2
	TIPC_HIGH_IMPORTANCE                        = 0x2
	TIPC_IMPORTANCE                             = 0x7f
	TIPC_LINK_STATE                             = 0x2
	TIPC_LOW_IMPORTANCE                         = 0x0
	TIPC_MAX_BEARER_NAME                        = 0x20
	TIPC_MAX_IF_NAME                            = 0x10
	TIPC_MAX_LINK_NAME                          = 0x44
	TIPC_MAX_MEDIA_NAME                         = 0x10
	TIPC_MAX_USER_MSG_SIZE                      = 0x101d0
	TIPC_MCAST_BROADCAST                        = 0x85
	TIPC_MCAST_REPLICAST                        = 0x86
	TIPC_MEDIUM_IMPORTANCE                      = 0x1
	TIPC_NODEID_LEN                             = 0x10
	TIPC_NODELAY                                = 0x8a
	TIPC_NODE_BITS                              = 0xc
	TIPC_NODE_MASK                              = 0xfff
	TIPC_NODE_OFFSET                            = 0x0
	TIPC_NODE_RECVQ_DEPTH                       = 0x83
	TIPC_NODE_SIZE                              = 0xfff
	TIPC_NODE_STATE                             = 0x0
	TIPC_OK                                     = 0x0
	TIPC_PUBLISHED                              = 0x1
	TIPC_REKEYING_NOW                           = 0xffffffff
	TIPC_RESERVED_TYPES                         = 0x40
	TIPC_RETDATA                                = 0x2
	TIPC_SERVICE_ADDR                           = 0x2
	TIPC_SERVICE_RANGE                          = 0x1
	TIPC_SOCKET_ADDR                            = 0x3
	TIPC_SOCK_RECVQ_DEPTH                       = 0x84
	TIPC_SOCK_RECVQ_USED                        = 0x89
	TIPC_SRC_DROPPABLE                          = 0x80
	TIPC_SUBSCR_TIMEOUT                         = 0x3
	TIPC_SUB_CANCEL                             = 0x4
	TIPC_SUB_PORTS                              = 0x1
	TIPC_SUB_SERVICE                            = 0x2
	TIPC_TOP_SRV                                = 0x1
	TIPC_WAIT_FOREVER                           = 0xffffffff
	TIPC_WITHDRAWN                              = 0x2
	TIPC_ZONE_BITS                              = 0x8
	TIPC_ZONE_CLUSTER_MASK                      = 0xfffff000
	TIPC_ZONE_MASK                              = 0xff000000
	TIPC_ZONE_OFFSET                            = 0x18
	TIPC_ZONE_SCOPE                             = 0x1
	TIPC_ZONE_SIZE                              = 0xff
	TMPFS_MAGIC                                 = 0x1021994
	TPACKET_ALIGNMENT                           = 0x10
	TPACKET_HDRLEN                              = 0x34
	TP_STATUS_AVAILABLE                         = 0x0
	TP_STATUS_BLK_TMO                           = 0x20
	TP_STATUS_COPY                              = 0x2
	TP_STATUS_CSUMNOTREADY                      = 0x8
	TP_STATUS_CSUM_VALID                        = 0x80
	TP_STATUS_GSO_TCP                           = 0x100
	TP_STATUS_KERNEL                            = 0x0
	TP_STATUS_LOSING                            = 0x4
	TP_STATUS_SENDING                           = 0x2
	TP_STATUS_SEND_REQUEST                      = 0x1
	TP_STATUS_TS_RAW_HARDWARE                   = 0x80000000
	TP_STATUS_TS_SOFTWARE                       = 0x20000000
	TP_STATUS_TS_SYS_HARDWARE                   = 0x40000000
	TP_STATUS_USER                              = 0x1
	TP_STATUS_VLAN_TPID_VALID                   = 0x40
	TP_STATUS_VLAN_VALID                        = 0x10
	TP_STATUS_WRONG_FORMAT                      = 0x4
	TRACEFS_MAGIC                               = 0x74726163
	TS_COMM_LEN                                 = 0x20
	UDF_SUPER_MAGIC                             = 0x15013346
	UDP_CORK                                    = 0x1
	UDP_ENCAP                                   = 0x64
	UDP_ENCAP_ESPINUDP                          = 0x2
	UDP_ENCAP_ESPINUDP_NON_IKE                  = 0x1
	UDP_ENCAP_GTP0                              = 0x4
	UDP_ENCAP_GTP1U                             = 0x5
	UDP_ENCAP_L2TPINUDP                         = 0x3
	UDP_GRO                                     = 0x68
	UDP_NO_CHECK6_RX                            = 0x66
	UDP_NO_CHECK6_TX                            = 0x65
	UDP_SEGMENT                                 = 0x67
	UDP_V4_FLOW                                 = 0x2
	UDP_V6_FLOW                                 = 0x6
	UMOUNT_NOFOLLOW                             = 0x8
	USBDEVICE_SUPER_MAGIC                       = 0x9fa2
	UTIME_NOW                                   = 0x3fffffff
	UTIME_OMIT                                  = 0x3ffffffe
	V9FS_MAGIC                                  = 0x1021997
	VERASE                                      = 0x2
	VINTR                                       = 0x0
	VKILL                                       = 0x3
	VLNEXT                                      = 0xf
	VMADDR_CID_ANY                              = 0xffffffff
	VMADDR_CID_HOST                             = 0x2
	VMADDR_CID_HYPERVISOR                       = 0x0
	VMADDR_CID_LOCAL                            = 0x1
	VMADDR_FLAG_TO_HOST                         = 0x1
	VMADDR_PORT_ANY                             = 0xffffffff
	VM_SOCKETS_INVALID_VERSION                  = 0xffffffff
	VQUIT                                       = 0x1
	VT0                                         = 0x0
	WAKE_MAGIC                                  = 0x20
	WALL                                        = 0x40000000
	WCLONE                                      = 0x80000000
	WCONTINUED                                  = 0x8
	WDIOC_SETPRETIMEOUT                         = 0xc0045708
	WDIOC_SETTIMEOUT                            = 0xc0045706
	WDIOF_ALARMONLY                             = 0x400
	WDIOF_CARDRESET                             = 0x20
	WDIOF_EXTERN1                               = 0x4
	WDIOF_EXTERN2                               = 0x8
	WDIOF_FANFAULT                              = 0x2
	WDIOF_KEEPALIVEPING                         = 0x8000
	WDIOF_MAGICCLOSE                            = 0x100
	WDIOF_OVERHEAT                              = 0x1
	WDIOF_POWEROVER                             = 0x40
	WDIOF_POWERUNDER                            = 0x10
	WDIOF_PRETIMEOUT                            = 0x200
	WDIOF_SETTIMEOUT                            = 0x80
	WDIOF_UNKNOWN                               = -0x1
	WDIOS_DISABLECARD                           = 0x1
	WDIOS_ENABLECARD                            = 0x2
	WDIOS_TEMPPANIC                             = 0x4
	WDIOS_UNKNOWN                               = -0x1
	WEXITED                                     = 0x4
	WGALLOWEDIP_A_MAX                           = 0x3
	WGDEVICE_A_MAX                              = 0x8
	WGPEER_A_MAX                                = 0xa
	WG_CMD_MAX                                  = 0x1
	WG_GENL_NAME                                = "wireguard"
	WG_GENL_VERSION                             = 0x1
	WG_KEY_LEN                                  = 0x20
	WIN_ACKMEDIACHANGE                          = 0xdb
	WIN_CHECKPOWERMODE1                         = 0xe5
	WIN_CHECKPOWERMODE2                         = 0x98
	WIN_DEVICE_RESET                            = 0x8
	WIN_DIAGNOSE                                = 0x90
	WIN_DOORLOCK                                = 0xde
	WIN_DOORUNLOCK                              = 0xdf
	WIN_DOWNLOAD_MICROCODE                      = 0x92
	WIN_FLUSH_CACHE                             = 0xe7
	WIN_FLUSH_CACHE_EXT                         = 0xea
	WIN_FORMAT                                  = 0x50
	WIN_GETMEDIASTATUS                          = 0xda
	WIN_IDENTIFY                                = 0xec
	WIN_IDENTIFY_DMA                            = 0xee
	WIN_IDLEIMMEDIATE                           = 0xe1
	WIN_INIT                                    = 0x60
	WIN_MEDIAEJECT                              = 0xed
	WIN_MULTREAD                                = 0xc4
	WIN_MULTREAD_EXT                            = 0x29
	WIN_MULTWRITE                               = 0xc5
	WIN_MULTWRITE_EXT                           = 0x39
	WIN_NOP                                     = 0x0
	WIN_PACKETCMD                               = 0xa0
	WIN_PIDENTIFY                               = 0xa1
	WIN_POSTBOOT                                = 0xdc
	WIN_PREBOOT                                 = 0xdd
	WIN_QUEUED_SERVICE                          = 0xa2
	WIN_READ                                    = 0x20
	WIN_READDMA                                 = 0xc8
	WIN_READDMA_EXT                             = 0x25
	WIN_READDMA_ONCE                            = 0xc9
	WIN_READDMA_QUEUED                          = 0xc7
	WIN_READDMA_QUEUED_EXT                      = 0x26
	WIN_READ_BUFFER                             = 0xe4
	WIN_READ_EXT                                = 0x24
	WIN_READ_LONG                               = 0x22
	WIN_READ_LONG_ONCE                          = 0x23
	WIN_READ_NATIVE_MAX                         = 0xf8
	WIN_READ_NATIVE_MAX_EXT                     = 0x27
	WIN_READ_ONCE                               = 0x21
	WIN_RECAL                                   = 0x10
	WIN_RESTORE                                 = 0x10
	WIN_SECURITY_DISABLE                        = 0xf6
	WIN_SECURITY_ERASE_PREPARE                  = 0xf3
	WIN_SECURITY_ERASE_UNIT                     = 0xf4
	WIN_SECURITY_FREEZE_LOCK                    = 0xf5
	WIN_SECURITY_SET_PASS                       = 0xf1
	WIN_SECURITY_UNLOCK                         = 0xf2
	WIN_SEEK                                    = 0x70
	WIN_SETFEATURES                             = 0xef
	WIN_SETIDLE1                                = 0xe3
	WIN_SETIDLE2                                = 0x97
	WIN_SETMULT                                 = 0xc6
	WIN_SET_MAX                                 = 0xf9
	WIN_SET_MAX_EXT                             = 0x37
	WIN_SLEEPNOW1                               = 0xe6
	WIN_SLEEPNOW2                               = 0x99
	WIN_SMART                                   = 0xb0
	WIN_SPECIFY                                 = 0x91
	WIN_SRST                                    = 0x8
	WIN_STANDBY                                 = 0xe2
	WIN_STANDBY2                                = 0x96
	WIN_STANDBYNOW1                             = 0xe0
	WIN_STANDBYNOW2                             = 0x94
	WIN_VERIFY                                  = 0x40
	WIN_VERIFY_EXT                              = 0x42
	WIN_VERIFY_ONCE                             = 0x41
	WIN_WRITE                                   = 0x30
	WIN_WRITEDMA                                = 0xca
	WIN_WRITEDMA_EXT                            = 0x35
	WIN_WRITEDMA_ONCE                           = 0xcb
	WIN_WRITEDMA_QUEUED                         = 0xcc
	WIN_WRITEDMA_QUEUED_EXT                     = 0x36
	WIN_WRITE_BUFFER                            = 0xe8
	WIN_WRITE_EXT                               = 0x34
	WIN_WRITE_LONG                              = 0x32
	WIN_WRITE_LONG_ONCE                         = 0x33
	WIN_WRITE_ONCE                              = 0x31
	WIN_WRITE_SAME                              = 0xe9
	WIN_WRITE_VERIFY                            = 0x3c
	WNOHANG                                     = 0x1
	WNOTHREAD                                   = 0x20000000
	WNOWAIT                                     = 0x1000000
	WSTOPPED                                    = 0x2
	WUNTRACED                                   = 0x2
	XATTR_CREATE                                = 0x1
	XATTR_REPLACE                               = 0x2
	XDP_COPY                                    = 0x2
	XDP_FLAGS_DRV_MODE                          = 0x4
	XDP_FLAGS_HW_MODE                           = 0x8
	XDP_FLAGS_MASK                              = 0x1f
	XDP_FLAGS_MODES                             = 0xe
	XDP_FLAGS_REPLACE                           = 0x10
	XDP_FLAGS_SKB_MODE                          = 0x2
	XDP_FLAGS_UPDATE_IF_NOEXIST                 = 0x1
	XDP_MMAP_OFFSETS                            = 0x1
	XDP_OPTIONS                                 = 0x8
	XDP_OPTIONS_ZEROCOPY                        = 0x1
	XDP_PACKET_HEADROOM                         = 0x100
	XDP_PGOFF_RX_RING                           = 0x0
	XDP_PGOFF_TX_RING                           = 0x80000000
	XDP_PKT_CONTD                               = 0x1
	XDP_RING_NEED_WAKEUP                        = 0x1
	XDP_RX_RING                                 = 0x2
	XDP_SHARED_UMEM                             = 0x1
	XDP_STATISTICS                              = 0x7
	XDP_TXMD_FLAGS_CHECKSUM                     = 0x2
	XDP_TXMD_FLAGS_TIMESTAMP                    = 0x1
	XDP_TX_METADATA                             = 0x2
	XDP_TX_RING                                 = 0x3
	XDP_UMEM_COMPLETION_RING                    = 0x6
	XDP_UMEM_FILL_RING                          = 0x5
	XDP_UMEM_PGOFF_COMPLETION_RING              = 0x180000000
	XDP_UMEM_PGOFF_FILL_RING                    = 0x100000000
	XDP_UMEM_REG                                = 0x4
	XDP_UMEM_TX_SW_CSUM                         = 0x2
	XDP_UMEM_UNALIGNED_CHUNK_FLAG               = 0x1
	XDP_USE_NEED_WAKEUP                         = 0x8
	XDP_USE_SG                                  = 0x10
	XDP_ZEROCOPY                                = 0x4
	XENFS_SUPER_MAGIC                           = 0xabba1974
	XFS_SUPER_MAGIC                             = 0x58465342
	ZONEFS_MAGIC                                = 0x5a4f4653
	_HIDIOCGRAWNAME_LEN                         = 0x80
	_HIDIOCGRAWPHYS_LEN                         = 0x40
	_HIDIOCGRAWUNIQ_LEN                         = 0x40
)

// Errors
const (
	E2BIG       = syscall.Errno(0x7)
	EACCES      = syscall.Errno(0xd)
	EAGAIN      = syscall.Errno(0xb)
	EBADF       = syscall.Errno(0x9)
	EBUSY       = syscall.Errno(0x10)
	ECHILD      = syscall.Errno(0xa)
	EDOM        = syscall.Errno(0x21)
	EEXIST      = syscall.Errno(0x11)
	EFAULT      = syscall.Errno(0xe)
	EFBIG       = syscall.Errno(0x1b)
	EINTR       = syscall.Errno(0x4)
	EINVAL      = syscall.Errno(0x16)
	EIO         = syscall.Errno(0x5)
	EISDIR      = syscall.Errno(0x15)
	EMFILE      = syscall.Errno(0x18)
	EMLINK      = syscall.Errno(0x1f)
	ENFILE      = syscall.Errno(0x17)
	ENODEV      = syscall.Errno(0x13)
	ENOENT      = syscall.Errno(0x2)
	ENOEXEC     = syscall.Errno(0x8)
	ENOMEM      = syscall.Errno(0xc)
	ENOSPC      = syscall.Errno(0x1c)
	ENOTBLK     = syscall.Errno(0xf)
	ENOTDIR     = syscall.Errno(0x14)
	ENOTTY      = syscall.Errno(0x19)
	ENXIO       = syscall.Errno(0x6)
	EPERM       = syscall.Errno(0x1)
	EPIPE       = syscall.Errno(0x20)
	ERANGE      = syscall.Errno(0x22)
	EROFS       = syscall.Errno(0x1e)
	ESPIPE      = syscall.Errno(0x1d)
	ESRCH       = syscall.Errno(0x3)
	ETXTBSY     = syscall.Errno(0x1a)
	EWOULDBLOCK = syscall.Errno(0xb)
	EXDEV       = syscall.Errno(0x12)
)

// Signals
const (
	SIGABRT = syscall.Signal(0x6)
	SIGALRM = syscall.Signal(0xe)
	SIGFPE  = syscall.Signal(0x8)
	SIGHUP  = syscall.Signal(0x1)
	SIGILL  = syscall.Signal(0x4)
	SIGINT  = syscall.Signal(0x2)
	SIGIOT  = syscall.Signal(0x6)
	SIGKILL = syscall.Signal(0x9)
	SIGPIPE = syscall.Signal(0xd)
	SIGQUIT = syscall.Signal(0x3)
	SIGSEGV = syscall.Signal(0xb)
	SIGTERM = syscall.Signal(0xf)
	SIGTRAP = syscall.Signal(0x5)
)
