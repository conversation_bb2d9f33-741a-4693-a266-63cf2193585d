package main

import (
	"encoding/base64"
	"fmt"
	"log"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware 创建一个 Gin 中间件来处理认证
func AuthMiddleware(expectedUser, expectedPass, expectedToken string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 从请求头中获取 Authorization 字段
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			// 如果没有认证头，拒绝访问
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Authorization header is required"})
			return
		}

		// 2. 解析认证头 (e.g., "Basic dXNlcjpwYXNz" or "Bearer token")
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid Authorization header format"})
			return
		}

		scheme := parts[0]
		credentials := parts[1]

		// 3. 根据不同的认证方案进行验证
		switch scheme {
		case "Basic":
			// 解码 Base64 凭证
			decodedBytes, err := base64.StdEncoding.DecodeString(credentials)
			if err != nil {
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid Base64 credentials"})
				return
			}

			// 分割用户名和密码
			userPass := strings.SplitN(string(decodedBytes), ":", 2)
			if len(userPass) != 2 {
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid Basic Auth credentials format"})
				return
			}

			// 验证用户名和密码
			if userPass[0] == expectedUser && userPass[1] == expectedPass {
				c.Next() // 认证成功，继续处理请求
			} else {
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid username or password"})
			}

		case "Bearer":
			// 验证 Bearer Token
			if credentials == expectedToken {
				c.Next() // 认证成功，继续处理请求
			} else {
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid bearer token"})
			}

		default:
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Unsupported authentication scheme"})
		}
	}
}

// HandleWebhook 是处理 SigNoz/Alertmanager 告警的 Gin Handler
func HandleWebhook(c *gin.Context) {
	var payload WebhookPayload

	// 1. 绑定 JSON 到我们的结构体
	// gin.Context.ShouldBindJSON 会自动处理请求体的读取和解析
	if err := c.ShouldBindJSON(&payload); err != nil {
		log.Printf("Error binding JSON: %v", err)
		// 如果 JSON 格式错误，返回一个清晰的错误信息
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload", "details": err.Error()})
		return
	}

	// 为了调试，我们可以打印出绑定的数据
	fmt.Println("--- [Received and Parsed Webhook Payload] ---")
	// 这里可以添加更详细的打印逻辑，如果需要的话
	log.Printf("Received alert group with status: %s for receiver: %s", payload.Status, payload.Receiver)
	fmt.Println("---------------------------------------------")

	// 2. 处理告警逻辑
	processAlerts(payload.Alerts)

	// 3. 返回成功的响应
	c.JSON(http.StatusOK, gin.H{"status": "success", "message": "Webhook received successfully"})
}

// processAlerts 是一个辅助函数，用于处理告警列表
func processAlerts(alerts []Alert) {
	// 遍历每一个具体的告警
	for _, alert := range alerts {
		fmt.Println("=======================================")
		log.Printf("  >> Alert Status: %s", alert.Status)

		// 从 Annotations 中提取告警摘要和描述
		summary, _ := alert.Annotations["summary"]
		description, _ := alert.Annotations["description"]
		log.Printf("  >> Summary: %s", summary)
		log.Printf("  >> Description: %s", description)

		// 打印所有标签，它们对于路由和上下文至关重要
		log.Println("  >> Labels:")
		for key, value := range alert.Labels {
			fmt.Printf("      - %s: %s\n", key, value)
		}
		fmt.Println("=======================================")

		// -------------------------------------------------------------------
		// 这是触发下一步行动的最佳位置！
		// 在这里，你可以根据 alert.Labels 或 alert.Status
		// 来决定是否以及如何调用你的 AI 智能体。
		//
		// 示例：
		// if alert.Status == "firing" && alert.Labels["severity"] == "critical" {
		//    go triggerAIAgent(alert) // 使用 goroutine 异步触发，避免阻塞 webhook 响应
		// }
		// -------------------------------------------------------------------
	}
}
